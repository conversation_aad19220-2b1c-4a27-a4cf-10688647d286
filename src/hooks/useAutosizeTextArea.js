'use client';
import { useEffect } from 'react'

const useAutosizeTextArea = (withAutosize, textAreaRef, value, maxHeight, initialHeight = 0) => {
  useEffect(() => {
    if (withAutosize && textAreaRef) {
      textAreaRef.style.height = initialHeight + 'px'
      const scrollHeight = textAreaRef.scrollHeight

      if (scrollHeight <= maxHeight) {
        textAreaRef.style.height = scrollHeight + 'px'
      } else {
        textAreaRef.style.height = maxHeight + 'px'
      }
    }
  }, [textAreaRef, value, withAutosize])
}

export default useAutosizeTextArea
