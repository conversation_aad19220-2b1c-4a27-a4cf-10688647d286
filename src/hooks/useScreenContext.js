import { createContext, useCallback, useEffect, useState } from 'react'

import useWindowWidth from './useWindowWidth'
import { isClient } from '../utils/isClient'

export const ScreenContext = createContext(undefined)

export const useScreenContext = (theme) => {
  const width = useWindowWidth()
  const {
    breakpoints,
    breakpoints: { xxl, xl, lg, md, sm, xs, xxs },
  } = theme

  const getHeight = () => (isClient ? document.documentElement.clientHeight : undefined)
  const height = getHeight()

  const getCurrentBreakpoint = useCallback(
    (setCurrentBreakpoint) => {
      for (const [key, value] of Object.entries(breakpoints)) {
        if (width && width >= value) {
          if (setCurrentBreakpoint) {
            setCurrentBreakpoint(key)
          } else {
            return key
          }
          break
        } else if (width && width < sm) {
          if (setCurrentBreakpoint) {
            setCurrentBreakpoint('xs')
          } else {
            return 'xs'
          }
        }
      }
    },
    [theme, width]
  )

  const [currentBreakpoint, setCurrentBreakpoint] = useState(getCurrentBreakpoint)

  useEffect(() => {
    getCurrentBreakpoint(setCurrentBreakpoint)
  }, [getCurrentBreakpoint])

  return { width, currentBreakpoint, xxl, xl, lg, md, sm, xs, xxs, height }
}
