import { useState, useEffect } from 'react'
import { isClient } from '../utils/isClient'

const useWindowHeight = () => {
  const [height, setHeight] = useState(isClient && window.innerHeight)

  useEffect(() => {
    if (isClient) {
      const cb = () => setHeight(window.innerHeight)

      window.addEventListener('resize', cb)

      return () => window.removeEventListener('resize', cb)
    }
  }, [])

  return height
}

export default useWindowHeight
