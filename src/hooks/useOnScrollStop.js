import { useEffect } from 'react'
import { isClient } from '../utils/isClient'

const useOnScrollStop = (callback, deps = [], timeout = 5) => {
  useEffect(() => {
    if (isClient) {
      const handleScroll = () => {
        setTimeout(callback, timeout)
      }

      window.addEventListener('scroll', handleScroll)
      return () => {
        window.removeEventListener('scroll', handleScroll)
      }
    }
  }, deps) // Empty array ensures that effect is only run on mount and unmount
}

export default useOnScrollStop
