'use client';
import React from 'react'
import clsx from 'clsx'

import { StyledTypography } from './styled'
import { TEXT_ALIGN_TYPES } from '../../types'
import Icon from '../Icon'

export type TypographyType =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'caption1'
  | 'caption2'
  | 'caption3'
  | 'body1'
  | 'body2'
  | 'link'
  | 'button1'
  | 'button2'

export interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  children?: React.ReactNode
  className?: string
  color?: string
  component?: React.ElementType<any>
  fontFamily?: string
  fontSize?: string
  fontWeight?: number | string
  iconName?: string
  iconProps?: Record<string, any>
  lineHeight?: string
  margin?: number | string
  padding?: number | string
  textAlign?: (typeof TEXT_ALIGN_TYPES)[number]
  textDecoration?: string
  textTransform?: string
  text?: React.ReactNode
  type?: TypographyType
}

export const defaultTypeMapping: Record<TypographyType, React.ElementType> = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  caption1: 'h6',
  caption2: 'h6',
  caption3: 'h6',
  body1: 'span',
  body2: 'span',
  link: 'span',
  button1: 'span',
  button2: 'span',
}

const Typography: React.FC<TypographyProps> = ({
  children,
  className,
  color,
  component,
  fontFamily,
  fontSize,
  fontWeight,
  iconName,
  iconProps,
  lineHeight,
  margin,
  padding,
  textAlign,
  textDecoration,
  textTransform,
  text,
  type = 'body1',
  ...otherProps
}) => {
  const Component: React.ElementType = component || defaultTypeMapping[type] || 'span'

  return (
    <StyledTypography
      as={Component}
      type={type}
      $textAlign={textAlign}
      $textDecoration={textDecoration}
      $textTransform={textTransform}
      color={color}
      margin={margin}
      padding={padding}
      className={clsx(className, type, 'typography')}
      fontWeight={fontWeight}
      fontFamily={fontFamily}
      fontSize={fontSize}
      $lineHeight={lineHeight}
      {...otherProps}
    >
      {iconName && <Icon name={iconName} {...iconProps} />}
      {children || text}
    </StyledTypography>
  )
}

export default Typography
