'use client'
import styled, { DefaultTheme } from 'styled-components'
import getTokens from '../../utils/getTokens'

interface StyledTypographyProps {
  type: string
  color?: string
  $textAlign?: string
  $textDecoration?: string
  $textTransform?: string
  fontWeight?: number | string
  fontFamily?: string
  fontSize?: string
  $lineHeight?: string
  margin?: number | string
  padding?: number | string
}

export const StyledTypography = styled.span<StyledTypographyProps>`
  ${({ type, theme }: { type: string; theme: DefaultTheme }) =>
    getTokens(`typography-${type}-black-small`, theme)};
  color: ${({ color }) => color};
  text-align: ${({ $textAlign }) => $textAlign};
  text-decoration: ${({ $textDecoration }) => $textDecoration};
  text-transform: ${({ $textTransform }) => $textTransform};
  font-weight: ${({ fontWeight }) => fontWeight};
  font-family: ${({ fontFamily }) => fontFamily};
  font-size: ${({ fontSize }) => fontSize};
  line-height: ${({ $lineHeight }) => $lineHeight};
  margin-block-start: 0;
  margin-block-end: 0;
  margin: ${({ margin }) => margin};
  padding: ${({ padding }) => padding};

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.sm}px) {
    ${({ type, theme }: { type: string; theme: DefaultTheme }) =>
      getTokens(`typography-${type}-black-medium`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    font-family: ${({ fontFamily }) => fontFamily};
    font-size: ${({ fontSize }) => fontSize};
    line-height: ${({ $lineHeight }) => $lineHeight};
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.lg}px) {
    ${({ type, theme }: { type: string; theme: DefaultTheme }) =>
      getTokens(`typography-${type}-black-large`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    font-family: ${({ fontFamily }) => fontFamily};
    font-size: ${({ fontSize }) => fontSize};
    line-height: ${({ $lineHeight }) => $lineHeight};
  }
`
