'use client';
import React from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledBadge, StyledBadgeWrapper } from './styled'
import capitalize from '../../utils/capitalize'

const Badge = ({
  anchorOrigin = {
    vertical: 'top',
    horizontal: 'right',
  },
  backgroundColor,
  badgeContent,
  children,
  className,
  color,
  invisible,
  ...otherProps
}) => {
  const { vertical, horizontal } = anchorOrigin
  return (
    <StyledBadgeWrapper className={className} {...otherProps}>
      {children}
      <StyledBadge
        className={clsx(`anchor${capitalize(vertical)}${capitalize(horizontal)}`, invisible && 'invisible')}
        backgroundColor={backgroundColor}
        color={color}
      >
        {badgeContent}
      </StyledBadge>
    </StyledBadgeWrapper>
  )
}

export default Badge

Badge.propTypes = {
  anchorOrigin: T.shape({ vertical: T.oneOf(['top', 'bottom']), horizontal: T.oneOf(['left', 'right']) }),
  backgroundColor: T.string,
  badgeContent: T.any,
  className: T.string,
  invisible: T.bool,
}
