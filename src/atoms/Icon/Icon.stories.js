import { color, number, select, withKnobs } from '@storybook/addon-knobs'
import React from 'react'
import styled from 'styled-components'

import Typography from '../Typography'
import Icon from './Icon'
import Icons from '../../icons'

export default {
  title: 'Atoms/Icon',
  component: Icon,
}

const StyledAllIcons = styled.div`
  display: flex;
  flex-wrap: wrap;

  .icon {
    margin-right: 10px;
    cursor: pointer;
  }
`
const iconsSorted = Object.keys(Icons).sort()

export const icon = () => (
  <Icon
    name={select('Name', iconsSorted, 'error')}
    width={number('Width', null)}
    height={number('Height', null)}
    wrapperWidth={number('Wrapper width', null)}
    wrapperHeight={number('Wrapper height', null)}
    fill={color('Fill', '')}
    stroke={color('Stroke', '')}
    strokeWidth={number('Stroke width', null)}
  />
)

export const allIcons = () => (
  <StyledAllIcons>
    {iconsSorted.map((name) => (
      <div style={{ marginRight: 15, marginTop: 10 }}>
        <Typography type="body2" text={name} />
        <Icon
          name={name}
          width={number('Width', 30)}
          height={number('Height', 30)}
          fill={color('Color', '')}
          stroke={color('Stroke', '')}
          strokeWidth={number('Stroke width', 2)}
        />
      </div>
    ))}
  </StyledAllIcons>
)

icon.story = {
  decorators: [withKnobs],
}

allIcons.story = {
  decorators: [withKnobs],
}
