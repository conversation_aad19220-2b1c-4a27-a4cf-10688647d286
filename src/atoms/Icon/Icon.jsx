import clsx from 'clsx'
import { PropTypes as T } from 'prop-types'
import React from 'react'
import { withTheme } from 'styled-components'

import Image from '../Image'
import { IconWrapper } from './styled'
import Icons from '../../icons'

const Icon = ({
  backgroundImage,
  backgroundSize,
  borderRadius,
  className,
  dataTooltip,
  disabled,
  disableImagekit,
  fill,
  height,
  imagekitParams,
  imagekitUrl,
  margin,
  name,
  onClick,
  size,
  style,
  title,
  theme,
  width,
  wrapperColor,
  wrapperComponent,
  wrapperHeight,
  wrapperWidth,
  strokeWidth,
  ...otherProps
}) => {
  const iconName = typeof name === 'string' ? name : name?.id || name?.slug
  const IconComponent = Icons[iconName]

  if (!backgroundImage && !IconComponent) return null

  return (
    <IconWrapper
      data-tooltip={dataTooltip}
      title={title}
      width={wrapperWidth}
      height={wrapperHeight}
      size={size}
      margin={margin}
      className={clsx(className, 'icon', disabled && 'disabled', backgroundImage && 'withBgImage')}
      onClick={disabled ? undefined : onClick}
      wrapperColor={wrapperColor}
      borderRadius={borderRadius}
      style={style}
      as={wrapperComponent}
    >
      {backgroundImage ? (
        <Image
          width={width}
          height={height}
          asBackground
          src={backgroundImage}
          radius={borderRadius}
          backgroundSize={backgroundSize}
          imagekitParams={imagekitParams}
          imagekitUrl={imagekitUrl}
          disableImagekit={disableImagekit || !imagekitUrl}
        />
      ) : (
        <IconComponent
          width={width}
          height={height}
          fill={fill}
          wrapperColor={wrapperColor}
          theme={theme}
          strokeWidth={strokeWidth}
          {...otherProps}
        />
      )}
    </IconWrapper>
  )
}

export default withTheme(Icon)

Icon.propTypes = {
  borderRadius: T.string,
  className: T.string,
  fill: T.string,
  height: T.number,
  margin: T.string,
  name: T.oneOfType([T.object, T.string]).isRequired,
  onClick: T.func,
  title: T.string,
  width: T.number,
  wrapperColor: T.string,
  wrapperHeight: T.oneOfType([T.number, T.string]),
  wrapperWidth: T.oneOfType([T.number, T.string]),
}
