'use client';
import React from 'react'
import { withTheme } from 'styled-components'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledTag } from './styled'
import Icon from '../Icon'

const Tag = ({
  backgroundColor,
  children,
  fontSize,
  fontWeight,
  iconRight,
  isOutlined,
  onCrossClick,
  removeProps = {},
  text,
  theme,
  type = 'gray',
  withBackgroundOpacity,
  withCrossIcon,
  color,
  ...rest
}) => (
  <StyledTag
    className={clsx(
      'tag',
      isOutlined && 'isOutlined',
      withBackgroundOpacity && 'withBackgroundOpacity',
      type
    )}
    backgroundColor={backgroundColor}
    fontSize={fontSize}
    fontWeight={fontWeight}
    color={color}
    type={type}
    {...rest}
  >
    {children || text}
    {iconRight}
    {withCrossIcon && (
      <Icon
        name="cross"
        height={8}
        width={8}
        fill={theme.components.tag?.standard?.[type]?.large?.color}
        className="crossIcon"
        onClick={onCrossClick}
        {...removeProps}
      />
    )}
  </StyledTag>
)

export default withTheme(Tag)

Tag.propsTypes = {
  backgroundColor: T.string,
  color: T.string,
  isOutlined: T.bool,
  type: T.oneOf(['primary', 'gray']),
  withBackgroundOpacity: T.bool,
  withCrossIcon: T.bool,
}
