import React from 'react'
import { boolean, select, text, withKnobs } from '@storybook/addon-knobs'

import Tag from './Tag'

export default {
  title: 'Atoms/Tag',
  component: Tag,
}

export const tag = () => (
  <Tag
    text={text('Text', 'Chosen item')}
    type={select('Type', ['primary', 'gray'], 'gray')}
    isOutlined={boolean('Is outlined', true)}
    withCrossIcon={boolean('With cross icon', true)}
  />
)

tag.story = {
  decorators: [withKnobs],
}
