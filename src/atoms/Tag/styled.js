'use client'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledTag = styled.span`
  ${({ type, theme }) => getTokens(`tag-standard-${type}-large`, theme)};
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 6px;
  font-family: ${({ theme }) => theme.font.family.primary};
  font-size: ${({ fontSize }) => fontSize}px;
  font-weight: ${({ fontWeight }) => fontWeight};
  margin-right: 3px;
  border: 1px solid transparent;

  &.isOutlined {
    background-color: ${({ theme }) => theme.color.general.light};
    color: ${({ theme }) => theme.color.general.dark};

    &.primary {
      border: 1px solid ${({ theme }) => theme.color.primary.main};
    }
    &.gray {
      border: 1px solid ${({ theme }) => theme.color.general.gray2};
    }

    .icon span {
      background-color: ${({ theme }) => theme.color.general.dark}; // for cross icon
    }
  }

  &.withBackgroundOpacity {
    &.primary {
      background-color: ${({ theme }) => theme.color.primary.main}1A;
      color: ${({ theme }) => theme.color.primary.main};
    }
  }

  background-color: ${({ backgroundColor }) => backgroundColor};
  color: ${({ color }) => color};

  .crossIcon {
    padding-left: 5px;
    cursor: pointer;

    div {
      background-color: transparent;
    }

    span {
      height: 1px;
      margin-bottom: -1px;
    }
  }
`
