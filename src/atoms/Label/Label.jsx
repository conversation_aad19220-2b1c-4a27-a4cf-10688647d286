'use client';
import React from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledLabel } from './styled'

const Label = ({
  as,
  children,
  className,
  color,
  fontSize,
  fontWeight,
  onClick,
  text,
  textTransform,
  type = 'bigger',
}) => {
  return (
    <StyledLabel
      as={as}
      color={color}
      fontWeight={fontWeight}
      fontSize={fontSize}
      $textTransform={textTransform}
      type={type}
      onClick={onClick}
      className={clsx(className, 'label')}
    >
      {children || text}
    </StyledLabel>
  )
}

export default Label

Label.propTypes = {
  fontWeight: T.number,
  textTransform: T.string,
  type: T.oneOf(['smaller', 'bigger', 'uppercase']),
}
