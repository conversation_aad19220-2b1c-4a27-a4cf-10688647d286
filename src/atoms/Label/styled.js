'use client'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledLabel = styled.div`
  display: inline;
  ${({ type, theme }) => getTokens(`label-standard-${type}-large`, theme)};
  color: ${({ color }) => color};
  font-weight: ${({ fontWeight }) => fontWeight};
  font-size: ${({ fontSize }) => fontSize};
  text-transform: ${({ $textTransform, type }) => $textTransform || (type === 'uppercase' && 'uppercase')};

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
    ${({ type, theme }) => getTokens(`label-standard-${type}-small`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    font-size: ${({ fontSize }) => fontSize};
    text-transform: ${({ $textTransform, type }) => $textTransform || (type === 'uppercase' && 'uppercase')};
  }
`
