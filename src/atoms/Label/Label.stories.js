import React from 'react'
import { withKnobs, text, select, color, number } from '@storybook/addon-knobs'

import Label from './Label'

export default {
  title: 'Atoms/Label', // Molecules / Organisms
  component: Label,
}

export const label = () => (
  <Label
    type={select('Type', ['bigger', 'smaller', 'uppercase'], 'bigger')}
    color={color('Color', '')}
    fontWeight={number('Font weight', undefined)}
  >
    {text('Text', 'Text')}
  </Label>
)

label.story = {
  decorators: [withKnobs],
}
