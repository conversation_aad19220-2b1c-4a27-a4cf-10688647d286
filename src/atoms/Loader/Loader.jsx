'use client';
import React from 'react'
import { PropTypes as T } from 'prop-types'

import LoaderCircular from './LoaderCircular'
import LoaderLinear from './LoaderLinear'

const Loader = ({ variant = 'circular', ...rest }) => {
  if (variant === 'circular') {
    return <LoaderCircular {...rest} />
  }
  return <LoaderLinear {...rest} />
}

export default Loader

Loader.propTypes = {
  variant: T.oneOf(['circular', 'linear']),
}
