import React from 'react'

import { StyledLoaderCircular, StyledOverlay } from './styled'

const LoaderCircular = ({ left, margin, position = 'absolute', size, top, withOverlay }) => {
  const Loader = (
    <StyledLoaderCircular
      position={position}
      margin={margin}
      top={top}
      left={left}
      withOverlay={withOverlay}
      size={size}
      className="loader"
    >
      <div className="loaderBg" />
      <div className="spinnerHolder animate1">
        <div className="loaderSpinner1" />
      </div>
      <div className="spinnerHolder animate2">
        <div className="loaderSpinner2" />
      </div>
    </StyledLoaderCircular>
  )

  return withOverlay ? <StyledOverlay>{Loader}</StyledOverlay> : Loader
}

export default LoaderCircular
