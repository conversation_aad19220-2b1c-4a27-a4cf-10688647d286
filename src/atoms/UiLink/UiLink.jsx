'use client';
import React, { useCallback } from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'
import { withTheme } from 'styled-components'
import { pick } from 'dot-object'

import { StyledLink } from './styled'
import Icon from '../Icon'

const Link = ({
  className,
  children,
  customColor,
  disabled,
  fontSize,
  fontWeight,
  hovered,
  href,
  inverted,
  iconName,
  iconPosition,
  isNavLink,
  isOuterLink,
  Link,
  NavLink,
  noStyles,
  onClick,
  rel,
  selected,
  target,
  text,
  textTransform,
  theme,
  themeColor = 'primary.main',
  to,
  withBorder,
  withHoverForTypographyOnly,
  ...otherProps
}) => {
  const getIcon = useCallback(
    () => (
      <Icon
        name={iconName}
        className="icon"
        fill={inverted ? theme.color.general.light : pick(themeColor, theme.color)}
      />
    ),
    [inverted]
  )

  return (
    <StyledLink
      theme={theme}
      customColor={customColor}
      themeColor={themeColor}
      inverted={inverted}
      $textTransform={textTransform}
      fontWeight={fontWeight}
      fontSize={fontSize}
      className={clsx(
        className,
        disabled && 'disabled',
        selected && 'selected',
        hovered && 'hovered',
        noStyles && 'noStyles',
        withBorder && 'withBorder',
        withHoverForTypographyOnly && 'withHoverForTypographyOnly',
        'link'
      )}
      onClick={disabled ? undefined : onClick}
      {...otherProps}
    >
      {iconPosition === 'left' && getIcon()}

      {isOuterLink || (!Link && !NavLink) ? (
        <a
          href={href}
          target={target || '_blank'}
          rel={rel || 'noreferrer noopener'}
          role="link"
          aria-disabled={disabled}
        >
          {children || text}
        </a>
      ) : (
        <Link
          as={isNavLink && NavLink}
          target={target}
          rel={rel}
          to={to}
          role="link"
          aria-disabled={disabled}
        >
          {children || text}
        </Link>
      )}
      {iconPosition === 'right' && getIcon()}
    </StyledLink>
  )
}

export default withTheme(Link)

Link.propTypes = {
  children: T.node,
  className: T.string,
  customColor: T.string,
  disabled: T.bool,
  fontSize: T.string,
  fontWeight: T.number,
  hovered: T.bool,
  href: T.string,
  inverted: T.bool,
  iconName: T.string,
  iconPosition: T.string,
  isNavLink: T.bool,
  isOuterLink: T.bool,
  noStyles: T.bool,
  onClick: T.func,
  rel: T.string,
  selected: T.bool,
  target: T.string,
  text: T.oneOfType([T.string, T.object]),
  theme: T.object,
  themeColor: T.oneOf([
    'primary.main',
    'secondary.main',
    'general.dark',
    'general.light',
    'general.gray3',
    'general.gray4',
    'general.gray5',
    'status.new',
    'status.error',
  ]),
  to: T.string,
  withBorder: T.bool,
  withHoverForTypographyOnly: T.bool,
}
