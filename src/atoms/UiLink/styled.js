'use client'
import styled from 'styled-components'
import { pick } from 'dot-object'

import { convertHexToRGBA } from '../../utils/convertHexToRgba'
import getTokens from '../../utils/getTokens'

export const StyledLink = styled.span`
  display: inline-flex;

  .icon {
    margin: 0 6px;
  }

  a {
    ${({ theme }) => getTokens('link-standard-main-large', theme)};
    color: ${({ theme, themeColor, customColor, inverted }) =>
      inverted ? theme.color.general.light : customColor || pick(themeColor, theme.color)};
    cursor: pointer;
    font-weight: ${({ fontWeight }) => fontWeight};
    text-transform: ${({ $textTransform }) => $textTransform};
    font-size: ${({ fontSize }) => fontSize};
  }

  &:hover:not(.disabled):not(.withHoverForTypographyOnly):not(.noStyles) a,
  &.hovered:not(.disabled):not(.withHoverForTypographyOnly):not(.noStyles) a {
    opacity: 0.7;
  }

  &.withHoverForTypographyOnly {
    &:hover:not(.disabled) a .typography,
    &.hovered:not(.disabled) a .typography {
      opacity: 0.7;
    }
  }

  &.withBorder:not(.noStyles) {
    a {
      border-bottom: ${({ theme, themeColor }) =>
        `1px solid ${convertHexToRGBA(pick(themeColor, theme.color), 0.7)}`};
    }
    &:not(.disabled) a:hover {
      border-bottom-color: ${({ theme, themeColor }) => pick(themeColor, theme.color)};
    }
  }

  &.disabled {
    cursor: not-allowed;
    a {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &.noStyles,
  a {
    text-decoration: none;
  }
`

//  &.selected a {
//   border-bottom: ${({ theme, inverted }) =>
//           `1px solid ${inverted ? theme.color.general.light : theme.color.general.dark}`};
// }
