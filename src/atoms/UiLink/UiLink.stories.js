import React from 'react'
import { withKnobs, select, boolean, text } from '@storybook/addon-knobs'

import UiLink from './UiLink'

export default {
  title: 'Atoms/UiLink',
  component: UiLink,
}

export const link = () => {
  const inverted = boolean('Inverted', false)
  return (
    <div style={inverted ? { backgroundColor: 'black', height: 25 } : {}}>
      <UiLink
        type="menu"
        themeColor={select('Color', ['general.gray4', 'primary.main', 'general.dark'], 'general.dark')}
        selected={boolean('Selected', false)}
        disabled={boolean('Disabled', false)}
        inverted={inverted}
        hovered={boolean('Hovered (menu opened)', false)}
        withBorder={boolean('With border', false)}
        withHoverForTypographyOnly={boolean('With hover for typography only', false)}
      >
        Menu link
      </UiLink>
    </div>
  )
}

link.story = {
  decorators: [withKnobs],
}
