import React from 'react'

export const statusCompleted = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
      status: { success },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 21" fill={fill || light}>
    <path
      d="M2 10.5C2 6.08172 5.58172 2.5 10 2.5C14.4183 2.5 18 6.08172 18 10.5C18 14.9183 14.4183 18.5 10 18.5C5.58172 18.5 2 14.9183 2 10.5Z"
      stroke={stroke || success}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.7929 8.70709L8.99999 12.5L7.70709 11.2071"
      stroke={stroke || success}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
