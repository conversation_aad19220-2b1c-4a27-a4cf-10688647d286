import React from 'react'

export const serverError = ({
  width,
  height,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 196} height={height || 177} viewBox="0 0 196 177">
    <path
      d="M97.8013 176.838C151.815 176.838 195.603 174.169 195.603 170.877C195.603 167.586 151.815 164.917 97.8013 164.917C43.7871 164.917 0 167.586 0 170.877C0 174.169 43.7871 176.838 97.8013 176.838Z"
      fill="#F3F3F3"
    />
    <path
      d="M0 116.825V137.289C0 139.276 1.58946 140.866 3.57628 140.866H192.026C194.013 140.866 195.603 139.276 195.603 137.289V116.825H0Z"
      fill="#E6E6E6"
    />
    <path
      d="M196 3.57628C196 1.58946 194.411 0 192.424 0H4.07317C2.08634 0 0.496887 1.58946 0.496887 3.57628V116.825H196V3.57628Z"
      fill="#434243"
    />
    <path d="M132.322 140.965H65.0685V161.628H132.322V140.965Z" fill="#C0C0C0" />
    <path d="M147.522 161.528H48.8758V171.164H147.522V161.528Z" fill="#E6E6E6" />
    <path d="M185.967 10.1328H10.6298V106.394H185.967V10.1328Z" fill={main} />
    <path
      d="M79.6716 29.0078L67.4526 41.2268L55.2337 29.0078L50.366 33.9749L62.5849 46.0945L50.366 58.3134L55.2337 63.1811L67.4526 51.0615L79.6716 63.1811L84.5393 58.3134L72.3204 46.0945L84.5393 33.9749L79.6716 29.0078Z"
      fill="#434243"
    />
    <path
      d="M146.131 33.9749L141.263 29.0078L129.044 41.2268L116.825 29.0078L111.958 33.9749L124.177 46.0945L111.958 58.3134L116.825 63.1811L129.044 51.0615L141.263 63.1811L146.131 58.3134L133.912 46.0945L146.131 33.9749Z"
      fill="#434243"
    />
    <path
      d="M124.375 92.9832C98.5467 75.2011 72.4199 92.7845 72.1219 92.9832L68.7443 88.1155C69.0423 87.9168 98.5467 67.9492 127.753 88.1155L124.375 92.9832Z"
      fill="#434243"
    />
  </svg>
)
