import React from 'react'

export const noImage = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray3 },
    },
  },
}) => (
  <svg width={width || 46} height={height || 46} viewBox="0 0 46 46" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.04849 14.9487C8.04849 13.1187 8.77545 11.3637 10.0694 10.0697C11.3634 8.77572 13.1184 8.04876 14.9484 8.04876C16.7784 8.04876 18.5334 8.77572 19.8274 10.0697C21.1214 11.3637 21.8483 13.1187 21.8483 14.9487C21.8483 16.7786 21.1214 18.5337 19.8274 19.8277C18.5334 21.1216 16.7784 21.8486 14.9484 21.8486C13.1184 21.8486 11.3634 21.1216 10.0694 19.8277C8.77545 18.5337 8.04849 16.7786 8.04849 14.9487Z"
      fill={fill || gray3}
      stroke={stroke}
      strokeWidth={strokeWidth}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.2428 0H27.5964C27.6497 0 27.7029 0.00185613 27.7559 0.00553266L32.7317 0.00553288C34.5827 0.00380798 36.111 0.00380779 37.3564 0.105582C38.6484 0.21253 39.8369 0.438503 40.9547 1.00775C42.6854 1.89007 44.0923 3.29762 44.9739 5.02867C45.5432 6.14301 45.7709 7.33324 45.8744 8.62698C45.9761 9.86896 45.9761 11.3973 45.9761 13.2499C45.9761 20.5688 46.0013 35.2133 46.0013 35.2133C46.0013 35.2133 46.0264 37.8987 45.6828 39.1794C45.2651 40.7395 44.444 42.1622 43.3022 43.3044C42.1603 44.4465 40.7379 45.268 39.1779 45.6861C38.0014 46 36.6421 46 34.8137 46H11.5696C10.9762 46 10.4018 46 9.91877 45.9672C9.45475 45.9534 9.02178 45.931 8.61814 45.8982C7.32613 45.7913 6.13762 45.5653 5.01983 44.9961C3.28916 44.1137 1.88221 42.7062 1.00063 40.9751C0.431391 39.8608 0.205419 38.6706 0.0984699 37.3768C-0.00157885 36.1348 -0.0015786 34.6048 -0.0015786 32.7539V13.2444C-0.00330358 11.3935 -0.00330383 9.86515 0.0984699 8.61972L0.0988986 8.61455C0.0849995 8.62158 0.077908 8.62525 0.077908 8.62525C0.184857 7.33324 0.410827 6.14473 0.98007 5.02694C1.86239 3.29627 3.26994 1.88932 5.001 1.00774C5.43387 0.786617 5.8782 0.617289 6.33559 0.486616C7.0619 0.276903 7.82116 0.16567 8.61986 0.101774C9.86185 0 11.3902 0 13.2428 0ZM7.66561 4.89162C7.43668 4.95388 7.25922 5.02446 7.10878 5.10076C6.24312 5.54107 5.53949 6.2447 5.09918 7.11036C4.91634 7.47088 4.76626 7.98665 4.68519 8.99404C4.59894 10.0307 4.59894 11.3693 4.59894 13.3392V32.659C4.59894 34.6289 4.59894 35.9693 4.68519 37.0042C4.76626 38.0116 4.91634 38.5274 5.09918 38.8879C5.51076 39.6954 6.15166 40.3633 6.94146 40.8078C7.10389 40.5587 7.28865 40.3249 7.49345 40.1092C7.8419 39.7332 8.31627 39.3019 8.80789 38.8551L28.376 21.0654C28.7452 20.7291 29.1126 20.3961 29.4473 20.1339C29.8724 19.7898 30.3547 19.523 30.8721 19.3456C31.7016 19.0762 32.5906 19.0481 33.4354 19.2646C33.9632 19.4081 34.4614 19.6441 34.9068 19.9615C35.242 20.1915 35.6069 20.4825 35.9792 20.7795L36.0349 20.8239L41.3756 25.0976L41.3756 13.3448C41.3756 11.3749 41.3756 10.0346 41.2894 8.99957C41.2083 7.99218 41.0582 7.47642 40.8754 7.11589C40.4351 6.25023 39.7314 5.5466 38.8658 5.10629C38.5053 4.92345 37.9895 4.77337 36.9821 4.6923C35.9454 4.60605 34.6068 4.60605 32.6369 4.60605L13.3171 4.60605C11.3472 4.60605 10.0069 4.60605 8.9719 4.6923C8.39736 4.73854 7.98273 4.80722 7.66561 4.89162Z"
      fill={fill || gray3}
      stroke={stroke}
      strokeWidth={strokeWidth}
    />
  </svg>
)
