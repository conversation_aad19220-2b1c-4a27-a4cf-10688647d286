import React from 'react'

export const clients = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      primary: { darker, main, lightest },
    },
  },
  wrapperColor,
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.9258 7.03329C20.9258 8.3385 19.8677 9.39658 18.5625 9.39658C17.2573 9.39658 16.1992 8.3385 16.1992 7.03329C16.1992 5.72809 17.2573 4.67001 18.5625 4.67001C19.8677 4.67001 20.9258 5.72809 20.9258 7.03329ZM22 17.4054V13.7575C22 12.1586 20.6912 10.8575 19.0824 10.8575H18.0855C16.4768 10.8575 15.168 12.1586 15.168 13.7575C15.168 14.6726 15.2959 15.8351 15.3963 16.7468C15.461 17.335 15.5143 17.8188 15.5143 18.0645C15.5143 18.4203 15.803 18.7091 16.1588 18.7091H20.6912C21.413 18.7091 22 18.1243 22 17.4054Z"
      fill={
        (fill === main && darker) ||
        (wrapperColor && wrapperColor !== main && `${light}A8`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.80078 7.03329C5.80078 8.3385 4.7427 9.39658 3.4375 9.39658C2.1323 9.39658 1.07422 8.3385 1.07422 7.03329C1.07422 5.72809 2.1323 4.67001 3.4375 4.67001C4.7427 4.67001 5.80078 5.72809 5.80078 7.03329ZM6.60446 16.7412C6.70471 15.8419 6.83203 14.6996 6.83203 13.7575C6.83203 12.1586 5.5232 10.8575 3.91445 10.8575H2.91758C1.30883 10.8575 0 12.1586 0 13.7575V17.4501C0 18.1445 0.566758 18.7091 1.26371 18.7091H5.84117C6.19695 18.7091 6.4857 18.4203 6.4857 18.0645C6.4857 17.8067 6.53938 17.3251 6.60446 16.7412Z"
      fill={
        (fill === main && darker) ||
        (wrapperColor && wrapperColor !== main && `${light}A8`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.7383 5.65829C14.7383 7.72289 13.0646 9.39658 11 9.39658C8.9354 9.39658 7.26172 7.72289 7.26172 5.65829C7.26172 3.5937 8.9354 1.92001 11 1.92001C13.0646 1.92001 14.7383 3.5937 14.7383 5.65829ZM16.8008 18.5793V16.0576C16.8008 13.1903 14.468 10.8575 11.6007 10.8575H10.3993C7.53199 10.8575 5.19922 13.1903 5.19922 16.0576V18.5793C5.19922 19.409 5.87426 20.0841 6.70398 20.0841H15.296C16.1257 20.0841 16.8008 19.409 16.8008 18.5793Z"
      fill={(fill === main && main) || (fill === light && light) || fill}
    />
  </svg>
)
