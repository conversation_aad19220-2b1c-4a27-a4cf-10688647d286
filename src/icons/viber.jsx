import React from 'react'

export const viber = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark, light },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <rect width="20" height="20" rx="5" fill="#8F5DB7" />
    <rect x="0.5" y="0.5" width="19" height="19" rx="4.5" stroke="#172B4D" strokeOpacity="0.1" />
    <path
      d="M11.251 4.375H8.75209C6.34684 4.375 4.37891 6.34293 4.37891 8.74818V10.6224C4.37891 12.3092 5.36287 13.8554 6.87787 14.5739V16.6668C6.87787 16.8542 7.11214 16.9479 7.23709 16.8073L9.04884 14.9956H11.251C13.6563 14.9956 15.6242 13.0276 15.6242 10.6224V8.74818C15.6242 6.34293 13.6563 4.375 11.251 4.375ZM12.891 12.5747L12.2506 13.1995C11.579 13.8554 9.84538 13.1057 8.31477 11.5439C6.78416 9.98204 6.11256 8.23277 6.75292 7.57679L7.37766 6.95205C7.61194 6.71777 8.0024 6.73339 8.26791 6.96767L9.17379 7.90478C9.50177 8.23277 9.36121 8.79503 8.93951 8.91998C8.64276 9.01369 8.43972 9.34168 8.53343 9.63843C8.68961 10.3256 9.56425 11.2003 10.2202 11.3721C10.517 11.4346 10.845 11.2784 10.9543 10.9816C11.0949 10.5599 11.6571 10.435 11.9695 10.763L12.8754 11.7001C13.1253 11.9187 13.1253 12.3092 12.891 12.5747ZM10.5638 7.34251C10.5014 7.34251 10.4389 7.34251 10.3764 7.35813C10.2671 7.37375 10.1578 7.28004 10.1421 7.17071C10.1265 7.06138 10.2202 6.95205 10.3296 6.93643C10.4076 6.92082 10.4857 6.92082 10.5638 6.92082C11.704 6.92082 12.6411 7.85792 12.6411 8.99808C12.6411 9.07617 12.6411 9.15426 12.6255 9.23235C12.6099 9.34168 12.5005 9.43539 12.3912 9.41978C12.2819 9.40416 12.1882 9.29483 12.2038 9.1855C12.2038 9.12302 12.2194 9.06055 12.2194 8.99808C12.235 8.0922 11.4853 7.34251 10.5638 7.34251ZM11.8133 9.01369C11.8133 9.12302 11.7196 9.21673 11.6103 9.21673C11.5009 9.21673 11.4072 9.12302 11.4072 9.01369C11.4072 8.56076 11.0324 8.18591 10.5795 8.18591C10.4701 8.18591 10.3764 8.0922 10.3764 7.98287C10.3764 7.87354 10.4701 7.77983 10.5795 7.77983C11.251 7.76421 11.8133 8.32648 11.8133 9.01369ZM13.4064 9.68529C13.3752 9.79462 13.2658 9.87271 13.1409 9.84147C13.0316 9.81024 12.9691 9.70091 13.0003 9.59158C13.0472 9.40416 13.0628 9.21673 13.0628 9.01369C13.0628 7.63927 11.9383 6.51473 10.5638 6.51473C10.5014 6.51473 10.4389 6.51473 10.3764 6.51473C10.2671 6.51473 10.1578 6.43664 10.1578 6.32731C10.1578 6.21798 10.2358 6.10865 10.3452 6.10865C10.4233 6.10865 10.5014 6.09303 10.5638 6.09303C12.1725 6.09303 13.4845 7.40499 13.4845 9.01369C13.4845 9.23235 13.4533 9.46663 13.4064 9.68529Z"
      fill={light || fill}
    />
  </svg>
)
