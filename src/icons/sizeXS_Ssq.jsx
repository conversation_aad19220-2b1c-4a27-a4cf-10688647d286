import React from 'react'

export const sizeXS_Ssq = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.5559 13.9783C17.1005 13.9783 16.6589 13.9179 16.2309 13.7972C15.8085 13.671 15.4683 13.5092 15.2104 13.3117L15.6631 12.3076C15.91 12.4887 16.2035 12.6341 16.5437 12.7438C16.8838 12.8535 17.224 12.9084 17.5641 12.9084C17.9427 12.9084 18.2225 12.8535 18.4036 12.7438C18.5846 12.6286 18.6751 12.4777 18.6751 12.2912C18.6751 12.154 18.6203 12.0415 18.5105 11.9538C18.4063 11.8605 18.2691 11.7864 18.0991 11.7316C17.9345 11.6767 17.7095 11.6163 17.4242 11.5505C16.9853 11.4463 16.6259 11.342 16.3461 11.2378C16.0663 11.1335 15.8249 10.9662 15.6219 10.7358C15.4244 10.5053 15.3257 10.1981 15.3257 9.81405C15.3257 9.47938 15.4162 9.17763 15.5972 8.90879C15.7783 8.63447 16.0499 8.41776 16.412 8.25865C16.7796 8.09954 17.2267 8.01999 17.7534 8.01999C18.121 8.01999 18.4804 8.06388 18.8315 8.15166C19.1826 8.23945 19.4899 8.36563 19.7532 8.53023L19.3417 9.54247C18.8095 9.24072 18.2774 9.08984 17.7452 9.08984C17.3721 9.08984 17.095 9.15019 16.914 9.2709C16.7384 9.3916 16.6506 9.5507 16.6506 9.74822C16.6506 9.94573 16.7521 10.0939 16.9551 10.1926C17.1636 10.2859 17.4791 10.3792 17.9015 10.4724C18.3405 10.5767 18.6998 10.6809 18.9796 10.7852C19.2594 10.8894 19.4981 11.054 19.6956 11.2789C19.8986 11.5039 20.0001 11.8084 20.0001 12.1924C20.0001 12.5216 19.9068 12.8234 19.7203 13.0977C19.5392 13.3665 19.2649 13.5805 18.8973 13.7396C18.5297 13.8987 18.0826 13.9783 17.5559 13.9783ZM6.43465 13.9783L4.99616 11.9099L3.5832 13.9783H2L4.20456 10.9481L2.11065 8.02H3.67683L5.04723 9.95218L6.39209 8.02H7.88166L5.80478 10.897L8.02636 13.9783H6.43465ZM14.7846 10.5735H13.0823V11.4247H14.7846V10.5735ZM8.74039 13.7972C9.16833 13.9179 9.60999 13.9783 10.0654 13.9783C10.5921 13.9783 11.0392 13.8987 11.4068 13.7396C11.7744 13.5805 12.0487 13.3665 12.2298 13.0977C12.4163 12.8234 12.5096 12.5216 12.5096 12.1924C12.5096 11.8084 12.4081 11.5039 12.2051 11.2789C12.0076 11.054 11.7689 10.8894 11.4891 10.7852C11.2093 10.6809 10.8499 10.5767 10.411 10.4724C9.98855 10.3792 9.67308 10.2859 9.4646 10.1926C9.2616 10.0939 9.1601 9.94574 9.1601 9.74823C9.1601 9.55072 9.24788 9.39161 9.42345 9.27091C9.6045 9.15021 9.88157 9.08986 10.2546 9.08986C10.7868 9.08986 11.319 9.24073 11.8512 9.54249L12.2627 8.53024C11.9993 8.36565 11.6921 8.23946 11.341 8.15168C10.9898 8.06389 10.6305 8.02 10.2629 8.02C9.73618 8.02 9.28903 8.09955 8.92144 8.25866C8.55933 8.41777 8.28776 8.63448 8.1067 8.9088C7.92565 9.17764 7.83513 9.47939 7.83513 9.81406C7.83513 10.1981 7.93388 10.5054 8.13139 10.7358C8.33439 10.9662 8.57579 11.1336 8.8556 11.2378C9.13541 11.342 9.49477 11.4463 9.93369 11.5505C10.219 11.6164 10.4439 11.6767 10.6085 11.7316C10.7786 11.7864 10.9158 11.8605 11.02 11.9538C11.1297 12.0416 11.1846 12.154 11.1846 12.2912C11.1846 12.4777 11.0941 12.6286 10.913 12.7438C10.732 12.8535 10.4522 12.9084 10.0736 12.9084C9.73343 12.9084 9.39327 12.8535 9.05311 12.7438C8.71296 12.6341 8.41943 12.4887 8.17254 12.3076L7.71991 13.3117C7.97777 13.5092 8.31793 13.671 8.74039 13.7972Z"
      fill={fill || dark}
    />
  </svg>
)
