import React from 'react'

export const stateDrafted = ({
  fill,
  width,
  height,
  stroke,
  strokeWidth,
  theme: {
    color: {
      general: { gray3, gray6 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M8.58325 18H4.58324C3.47867 17.9999 2.58324 17.1045 2.58325 15.9999L2.58333 3.99999C2.58334 2.89542 3.47876 2 4.58333 2H13.5836C14.6881 2 15.5836 2.89543 15.5836 4V8M6.08356 6H12.0836M6.08356 9H12.0836M6.08356 12H9.08356"
      stroke={stroke || gray6}
      strokeWidth={strokeWidth || 1.4}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.5801 15.207L15.8227 10.9644L18.6511 13.7928L14.4085 18.0354H11.5801V15.207Z"
      stroke={gray3}
      strokeWidth={strokeWidth || 1.4}
      strokeLinejoin="round"
    />
  </svg>
)
