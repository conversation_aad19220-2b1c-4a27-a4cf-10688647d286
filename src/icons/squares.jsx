import React from 'react'

export const squares = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M9.4441 1.89725C9.75112 1.59024 10.2489 1.59024 10.5559 1.89725L12.7984 4.13975C13.1054 4.44677 13.1054 4.94454 12.7984 5.25155L10.5559 7.49405C10.2489 7.80106 9.75112 7.80106 9.4441 7.49405L7.20161 5.25155C6.89459 4.94454 6.89459 4.44676 7.20161 4.13975L9.4441 1.89725Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M14.7488 7.20193C15.0558 6.89491 15.5536 6.89491 15.8606 7.20193L18.1031 9.44442C18.4101 9.75144 18.4101 10.2492 18.1031 10.5562L15.8606 12.7987C15.5536 13.1057 15.0558 13.1057 14.7488 12.7987L12.5063 10.5562C12.1993 10.2492 12.1993 9.75144 12.5063 9.44442L14.7488 7.20193Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M4.13943 7.20193C4.44644 6.89491 4.94422 6.89491 5.25123 7.20193L7.49373 9.44442C7.80074 9.75144 7.80074 10.2492 7.49373 10.5562L5.25123 12.7987C4.94422 13.1057 4.44644 13.1057 4.13943 12.7987L1.89693 10.5562C1.58992 10.2492 1.58992 9.75144 1.89693 9.44442L4.13943 7.20193Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M9.4441 12.5066C9.75112 12.1996 10.2489 12.1996 10.5559 12.5066L12.7984 14.7491C13.1054 15.0561 13.1054 15.5539 12.7984 15.8609L10.5559 18.1034C10.2489 18.4104 9.75112 18.4104 9.4441 18.1034L7.20161 15.8609C6.89459 15.5539 6.89459 15.0561 7.20161 14.7491L9.4441 12.5066Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
