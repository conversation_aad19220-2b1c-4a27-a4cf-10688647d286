import React from 'react'

export const ruler = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M14.8002 6V10M5.20017 6V9.2M8.44017 6L8.40017 10M11.6002 6V8.4M4.40017 14H15.6002C17.2002 14 18.0002 13.2 18.0002 11.6V8.4C18.0002 6.8 17.2002 6 15.6002 6H4.40017C2.80017 6 2.00017 6.8 2.00017 8.4V11.6C2.00017 13.2 2.80017 14 4.40017 14Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
    />
  </svg>
)
