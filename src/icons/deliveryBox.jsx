import React from 'react'

export const deliveryBox = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M2.6416 6.2002L9.99992 10.4585L17.3082 6.22517M10 18.0085V10.4502M14.1666 11.0337V7.98369L6.2583 3.41699M8.2755 2.06699L3.82551 4.54202C2.81717 5.10035 1.99219 6.50034 1.99219 7.65034V12.3587C1.99219 13.5087 2.81717 14.9087 3.82551 15.467L8.2755 17.942C9.2255 18.467 10.7838 18.467 11.7338 17.942L16.1839 15.467C17.1922 14.9087 18.0172 13.5087 18.0172 12.3587V7.65034C18.0172 6.50034 17.1922 5.10035 16.1839 4.54202L11.7338 2.06699C10.7755 1.53366 9.2255 1.53366 8.2755 2.06699Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
