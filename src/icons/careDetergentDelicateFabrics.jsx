import React from 'react'

export const careDetergentDelicateFabrics = ({
  width,
  height,
  fill,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.6343 11.9398C20.4154 11.5786 20.1254 11.2878 19.8406 11.0097C19.8781 10.9715 19.9148 10.934 19.9515 10.8966C20.1434 10.7024 20.3413 10.5008 20.5031 10.273C21.1259 9.39312 21.377 8.40756 21.2496 7.34331C21.1956 6.88838 21.1057 6.5054 20.9761 6.17338C20.5976 5.20132 20.0002 4.43536 19.2006 3.89724C18.8116 3.63567 18.4219 3.44755 18.0089 3.32089C17.6454 3.20997 17.2647 3.15376 16.8779 3.15376C16.5901 3.15376 16.2904 3.18524 15.9861 3.2467C15.8002 3.28417 15.6218 3.33214 15.433 3.38235C15.4157 3.38685 15.3977 3.39134 15.3805 3.39659C15.1886 2.50772 14.7712 1.76574 14.1386 1.19089C13.8014 0.885109 13.4079 0.62879 12.935 0.407696C12.3564 0.137886 11.7163 0.000732422 11.0321 0.000732422C10.7652 0.000732422 10.4834 0.0217176 10.1964 0.0621891C9.50164 0.161119 8.83985 0.436925 8.22978 0.88361C7.43609 1.4637 6.91746 2.23566 6.64315 3.2437C6.63041 3.29166 6.61692 3.33963 6.60418 3.38835C6.16574 3.2332 5.68308 3.16051 5.10673 3.16051C5.04003 3.16051 4.97333 3.16126 4.90663 3.16275C4.31529 3.17774 3.7352 3.35312 3.08016 3.71661C2.62748 3.96769 2.21602 4.31919 1.85702 4.76063C1.6734 4.98622 1.46505 5.25828 1.30841 5.56781C1.03185 6.11492 0.853481 6.67853 0.763544 7.28935C0.694593 7.75777 0.717826 8.23968 0.835493 8.80628C0.973396 9.47031 1.27618 10.0556 1.73561 10.5481C1.87126 10.6934 2.01291 10.8366 2.15007 10.9753C2.15981 10.9857 2.1703 10.9955 2.18005 11.006L2.10285 11.0824C2.07962 11.1057 2.05563 11.1281 2.03165 11.1514C1.97319 11.2076 1.91248 11.266 1.85478 11.3298L1.81355 11.3755C1.70863 11.4916 1.6007 11.6123 1.50327 11.7442C0.799519 12.7058 0.568681 13.836 0.816007 15.1026C0.937421 15.7224 1.18999 16.31 1.56623 16.8481C2.09386 17.6028 2.72716 18.141 3.50286 18.4947C4.03349 18.736 4.5896 18.8589 5.15695 18.8589C5.54967 18.8589 5.95739 18.8005 6.36885 18.6843C6.44754 18.6626 6.52623 18.6394 6.60568 18.6154C6.61767 18.6641 6.63041 18.712 6.64315 18.76C6.91296 19.7396 7.39038 20.4816 8.10237 21.0302C8.77765 21.5503 9.49489 21.8598 10.2346 21.9505C10.5112 21.9842 10.7682 22.0007 11.0201 22.0007C11.7231 22.0007 12.3489 21.8696 12.932 21.599C14.1948 21.0144 14.9795 20.1046 15.331 18.8192C15.3468 18.7615 15.3528 18.7046 15.358 18.6543C15.3603 18.6341 15.3618 18.6169 15.364 18.6026C15.801 18.7765 16.2746 18.8642 16.7753 18.8642C17.0106 18.8642 17.2564 18.8447 17.5053 18.8065C18.544 18.6468 19.4599 18.1065 20.1554 17.2431C21.0165 16.1736 21.383 14.9812 21.2459 13.6973C21.1807 13.0858 20.9805 12.5109 20.6343 11.9413V11.9398ZM19.3639 9.36688C19.2021 9.60821 18.9712 9.81132 18.7269 10.0264C18.6714 10.0751 18.616 10.1238 18.562 10.1726C18.4623 10.2625 18.3289 10.2812 18.2315 10.2812C16.7453 10.2767 15.2336 10.2767 13.7721 10.276C13.2887 10.276 12.8053 10.276 12.3219 10.276L15.3752 4.97573C15.8624 4.71491 16.3331 4.58825 16.812 4.58825C16.9184 4.58825 17.0256 4.595 17.1328 4.60774C17.7061 4.67669 18.2337 4.92326 18.7014 5.34222C19.1024 5.70122 19.4022 6.13141 19.5918 6.62157C19.9845 7.6326 19.9073 8.5567 19.3639 9.36688V9.36688ZM18.4114 11.7375C19.3227 12.3565 19.7694 13.0678 19.8159 13.9701C19.8721 15.0696 19.4734 15.9937 18.6302 16.7177C18.0883 17.1831 17.4963 17.4192 16.8697 17.4192C16.5984 17.4192 16.3166 17.3742 16.0318 17.2866C15.8692 17.2363 15.7035 17.1734 15.5439 17.1127C15.4937 17.0939 15.4435 17.0745 15.394 17.0565L12.3204 11.7375H18.4114V11.7375ZM6.69112 5.0904C7.0666 5.7267 7.44284 6.37874 7.80633 7.00829L7.8483 7.08024C8.44863 8.12126 9.04821 9.16228 9.64779 10.204C9.66128 10.228 9.67627 10.2505 9.69125 10.2745C9.09917 10.2775 8.45538 10.279 7.65194 10.279C7.24423 10.279 6.83652 10.279 6.42805 10.279C6.01884 10.279 5.60963 10.279 5.19967 10.279C4.67504 10.279 4.15041 10.279 3.62353 10.2812C2.89504 9.83081 2.44986 9.25896 2.26324 8.53497C2.18829 8.24492 2.19054 7.93839 2.19878 7.64984C2.21002 7.26087 2.30821 6.86814 2.49782 6.44919C2.78637 5.81289 3.2443 5.30999 3.86036 4.95399C4.27932 4.71191 4.70727 4.59425 5.16669 4.59425C5.22215 4.59425 5.27911 4.59574 5.33532 4.59949C5.74004 4.62497 6.14101 4.73814 6.56071 4.945C6.58844 4.95849 6.6394 5.00571 6.69037 5.0919L6.69112 5.0904ZM10.4827 8.6991C10.359 8.47726 10.2309 8.24867 10.099 8.02608C9.81792 7.55316 9.53761 7.06825 9.26705 6.59908L9.03472 6.19662C8.78514 5.76492 8.53632 5.33322 8.283 4.89254L7.97047 4.35067C7.93599 3.56822 8.20655 2.88095 8.77615 2.30611C9.15688 1.92163 9.64179 1.66156 10.2166 1.5334C10.4917 1.47195 10.7637 1.44122 11.0261 1.44122C11.5859 1.44122 12.1195 1.58212 12.6127 1.86017C13.3704 2.28737 13.8291 2.93492 13.9752 3.78482C13.9827 3.82829 13.991 3.87176 13.9992 3.91522C14.0142 3.99317 14.0277 4.06662 14.0374 4.13932C14.0412 4.17079 14.0404 4.21127 14.0389 4.26673C14.0389 4.28396 14.0382 4.3027 14.0374 4.32294L10.9976 9.58798C10.8132 9.29194 10.6461 8.9914 10.4819 8.69835L10.4827 8.6991ZM3.41293 11.8581C3.53959 11.7667 3.66175 11.724 3.79591 11.724V11.4991L3.79741 11.724C5.21391 11.7277 6.65439 11.7285 8.04691 11.7285C8.59478 11.7285 9.14189 11.7285 9.68976 11.7285C9.61481 11.8754 9.52787 12.0155 9.43868 12.1617C9.35699 12.2951 9.2723 12.433 9.1966 12.5761C9.04746 12.8594 8.88183 13.1427 8.72144 13.417C8.66673 13.5107 8.61201 13.6044 8.5573 13.6981C8.45013 13.8832 8.34146 14.0683 8.23353 14.2534C8.12261 14.4423 8.01169 14.6319 7.90227 14.8215C7.79284 15.0112 7.68417 15.2015 7.5755 15.3926C7.46907 15.5785 7.36339 15.7651 7.25622 15.9502C7.10633 16.2103 6.95418 16.4689 6.79679 16.7379L6.61842 17.043C6.10128 17.294 5.64411 17.4117 5.18393 17.4117H5.13971C4.49367 17.4005 3.91058 17.1674 3.35522 16.699C2.79986 16.2298 2.42662 15.6055 2.2445 14.8425C2.17405 14.5457 2.15831 14.2879 2.19653 14.0541C2.21227 13.9596 2.21827 13.8682 2.22426 13.779C2.23176 13.6726 2.2385 13.5714 2.26024 13.4822C2.41838 12.8354 2.79536 12.3041 3.41218 11.8581H3.41293ZM7.97871 17.7647C7.97946 17.7272 7.98021 17.6868 7.98096 17.6448L10.9803 12.442C11.6047 13.4283 13.5405 16.7417 14.0352 17.686C14.0015 19.0883 13.1726 20.1241 11.8085 20.4621C11.5155 20.5348 11.2247 20.5715 10.9444 20.5715C10.1754 20.5715 9.46866 20.2994 8.8436 19.7621C8.3482 19.3371 8.05816 18.7518 7.98246 18.0225C7.97496 17.9468 7.97646 17.8666 7.97871 17.7647V17.7647Z"
      fill={fill || main}
    />
  </svg>
)
