import React from 'react'

export const careDelicate40Degrees = ({
  width,
  height,
  fill,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 28} height={height || 23} viewBox="0 0 28 23" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.09443 14.9502L2.69925 5.90955C2.45995 5.04551 2.45571 4.81891 3.06774 4.39536C4.58193 3.34707 6.89663 3.38731 8.64166 4.24499C9.08274 4.46119 9.26212 4.61104 9.47063 4.78523C9.61752 4.90794 9.77886 5.04273 10.0563 5.22128C11.1009 5.88372 12.2985 6.26589 13.5337 6.33098C15.7209 6.45297 17.1608 5.60632 18.5062 4.8152C20.1738 3.83461 21.6964 2.93934 24.3172 4.07981C25.6574 4.66388 25.5147 5.13705 25.2058 6.16097C25.1306 6.41047 25.0454 6.69267 24.9695 7.01713L23.0635 14.4949C22.8431 15.1493 22.5572 15.135 21.8886 15.1016C21.7415 15.0942 21.5759 15.0859 21.3884 15.0836C20.4346 15.0698 18.608 15.0819 16.5098 15.0958C11.844 15.1267 5.83507 15.1664 5.09443 14.9502ZM19.529 2.43644C19.1233 2.56503 18.6396 2.86626 18.0834 3.21262C16.3405 4.29802 13.886 5.8266 10.8886 3.87439C10.6527 3.72047 10.5222 3.60611 10.398 3.49729C10.2334 3.35307 10.0798 3.21856 9.70689 3.01458C6.65266 1.35418 4.46668 2.13227 3.00884 2.65118C2.59056 2.80007 2.23222 2.92762 1.93051 2.97011C1.03258 -1.22303 -0.0686484 1.06413 0.00335513 1.6444L3.42776 15.0752C3.46459 15.23 3.49416 15.3731 3.52134 15.5046C3.67706 16.2579 3.75433 16.6317 4.66664 16.6677C7.45784 16.7821 23.5845 16.7249 23.8492 16.5576C23.9629 16.4691 24.0078 16.4493 24.0305 16.4393C24.038 16.4361 24.043 16.4338 24.0473 16.4306C24.0563 16.4238 24.062 16.4124 24.0796 16.377C24.0989 16.3383 24.1326 16.2708 24.2008 16.1489L28 1.0599C26.6588 -0.0290594 26.4234 1.11556 26.1756 2.32096L26.1756 2.32101L26.1756 2.32106L26.1756 2.32109C26.1342 2.52216 26.0926 2.72492 26.0453 2.91929C25.5947 2.91003 25.1435 2.73579 24.6532 2.54644C24.1344 2.34608 23.5717 2.1288 22.9195 2.07219C21.7778 1.94691 20.6224 2.0675 19.5311 2.42585L19.529 2.43644ZM17.4621 12.3348C16.7082 12.6313 15.7277 12.7287 15.6451 11.7884C15.6106 10.9156 15.629 10.0415 15.7002 9.17085C16.2656 8.95907 16.956 8.99931 17.5723 9.11155L17.5792 9.18775C17.6649 10.1225 17.8022 11.6197 17.4621 12.3348ZM14.6858 7.63336C14.0451 8.0443 14.0601 8.76828 14.0769 9.57859L14.0769 9.57867C14.0792 9.68703 14.0814 9.79693 14.0822 9.90783C14.0857 10.3966 14.0704 10.8428 14.0567 11.2455C13.9954 13.0377 13.9636 13.9689 15.7446 13.9654C16.0382 13.9654 16.3236 13.9809 16.5968 13.9957C18.5055 14.0989 19.8204 14.1701 19.1775 8.71977C19.0568 7.69689 18.6015 7.44912 17.4833 7.44912C17.2971 7.44912 17.0771 7.43818 16.8416 7.42647C16.0781 7.38851 15.1528 7.34251 14.6963 7.63548L14.6858 7.63336ZM11.6293 7.41466C11.3997 7.41466 11.1835 7.52277 11.0457 7.70646L8.36612 11.2802C8.27598 11.4004 8.22726 11.5466 8.22726 11.6968C8.22726 12.0804 8.53821 12.3913 8.92178 12.3913H11.0986C11.1716 12.3913 11.2308 12.4505 11.2308 12.5236V13.3244C11.2308 13.7216 11.5549 14.0424 11.952 14.0384C12.3435 14.0344 12.6588 13.7159 12.6588 13.3244V12.5236C12.6588 12.4505 12.718 12.3913 12.791 12.3913H12.9476C13.2874 12.3913 13.5629 12.1159 13.5629 11.776C13.5629 11.4362 13.2874 11.1607 12.9476 11.1607H12.791C12.718 11.1607 12.6588 11.1015 12.6588 11.0285V8.14399C12.6588 7.74119 12.3322 7.41466 11.9294 7.41466H11.6293ZM10.0039 11.1613L11.2308 9.57088V11.0302C11.2308 11.1033 11.1715 11.1625 11.0984 11.1624L10.0039 11.1613ZM4.58748 18.2141C4.00182 19.8786 5.11422 19.8133 6.60144 19.7259L6.60148 19.7259C6.95754 19.705 7.33508 19.6828 7.71595 19.6828C8.3936 19.6828 9.4839 19.6935 10.7838 19.7063H10.7839H10.784C15.2777 19.7503 22.2761 19.8189 23.3923 19.5555C23.603 18.8701 23.598 18.8571 23.4512 18.4746L23.4512 18.4746C23.4221 18.3987 23.3874 18.3082 23.3477 18.195C21.707 17.9361 6.01376 17.9276 4.58748 18.2141ZM6.60145 22.557C5.11422 22.6443 4.00182 22.7097 4.58748 21.0452C6.01376 20.7586 21.707 20.7671 23.3477 21.0261C23.3874 21.1392 23.4221 21.2297 23.4512 21.3056C23.598 21.6882 23.603 21.7012 23.3923 22.3865C22.2761 22.6499 15.2776 22.5813 10.7839 22.5373C9.48395 22.5246 8.39361 22.5139 7.71595 22.5139C7.33507 22.5139 6.95751 22.5361 6.60145 22.557Z"
      fill={fill || main}
    />
  </svg>
)
