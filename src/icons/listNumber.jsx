import React from 'react'

export const listNumber = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M9 4H18M9 10H18M10 16H18M2 14C2 13.4696 2.21071 12.9609 2.58579 12.5858C2.96086 12.2107 3.46957 12 4 12C4.53043 12 5.03914 12.2107 5.41421 12.5858C5.78929 12.9609 6 13.4696 6 14C6 14.591 5.5 15 5 15.5L2 18H6M4 8V2L2 4"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
