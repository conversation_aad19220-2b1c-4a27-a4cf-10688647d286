import React from 'react'

export const faqs = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      primary: { dark, main, lightest },
    },
  },
  wrapperColor,
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      d="M7.94216 8H19.0578C20.1305 8 21 8.87304 21 9.94994V17.0076C21 18.0845 20.1305 18.9576 19.0578 18.9576V20.4873C19.0578 20.9155 18.5654 21.1543 18.2313 20.8881L15.8083 18.9576H7.9421C6.86947 18.9576 6 18.0845 6 17.0076V9.94994C6.00006 8.87304 6.86953 8 7.94216 8Z"
      fill={
        (fill === main && dark) ||
        (wrapperColor && wrapperColor !== main && `${light}A8`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <g opacity={fill === light || '0.75'}>
      <path
        d="M11.6846 11.2789C11.9913 11.0151 12.6599 10.6349 13.5247 10.6349C14.6226 10.6349 15.4261 11.2972 15.4261 12.2296C15.4261 13.162 14.4754 13.6097 14.2914 13.6894L14.2729 14.0083C14.2729 14.1433 14.1626 14.2537 14.0276 14.2537H13.1444C13.0094 14.2537 12.899 14.1433 12.899 14.0083L12.8561 13.2416C12.8561 13.1681 12.9174 13.1312 13.0033 13.1067C13.4142 12.9901 13.77 12.7448 13.77 12.4749C13.77 12.2603 13.6105 12.107 13.3407 12.107C13.0524 12.107 12.715 12.2848 12.5003 12.4503C12.4575 12.4811 12.4084 12.5056 12.3593 12.5056C12.2796 12.5056 12.2121 12.4626 12.163 12.3952L11.6233 11.6162C11.5925 11.5732 11.5742 11.5119 11.5742 11.4629C11.5743 11.3831 11.6233 11.3341 11.6846 11.2789Z"
        fill={light || fill}
      />
      <path
        d="M12.8499 15.2039C12.8499 15.1057 12.9359 15.0198 13.0339 15.0198H14.138C14.2362 15.0198 14.3221 15.1057 14.3221 15.2039V16.0625C14.3221 16.1607 14.2361 16.2466 14.138 16.2466H13.0339C12.9358 16.2466 12.8499 16.1607 12.8499 16.0625V15.2039Z"
        fill={light || fill}
      />
    </g>
    <path
      d="M14.0578 1H2.94216C1.86953 1 1 1.87304 1 2.94994V10.0076C1 11.0845 1.86953 11.9576 2.94216 11.9576V13.4873C2.94216 13.9155 3.43456 14.1543 3.76868 13.8881L6.1917 11.9576H14.0579C15.1305 11.9576 16 11.0845 16 10.0076V2.94994C15.9999 1.87304 15.1305 1 14.0578 1Z"
      fill={(fill === main && main) || (fill === light && light) || fill}
    />
    <path
      d="M6.68438 4.2789C6.99109 4.01513 7.6597 3.63486 8.52448 3.63486C9.62236 3.63486 10.4259 4.29722 10.4259 5.22959C10.4259 6.16195 9.47518 6.60968 9.29115 6.68941L9.27269 7.00828C9.27269 7.14325 9.16235 7.25372 9.02738 7.25372H8.14414C8.00912 7.25372 7.89871 7.14325 7.89871 7.00828L7.85588 6.24162C7.85588 6.16806 7.91716 6.13116 8.00301 6.10665C8.41395 5.99014 8.76979 5.74483 8.76979 5.4749C8.76979 5.26026 8.61026 5.10696 8.34045 5.10696C8.05213 5.10696 7.71474 5.28476 7.50011 5.45034C7.45721 5.48107 7.40815 5.50557 7.35909 5.50557C7.27936 5.50557 7.21184 5.46262 7.16278 5.39516L6.62304 4.61616C6.5923 4.57321 6.57397 4.51192 6.57397 4.46286C6.57404 4.38313 6.6231 4.33413 6.68438 4.2789Z"
      fill={
        (fill === main && light) ||
        (wrapperColor && wrapperColor !== main && wrapperColor) ||
        (fill === light && main) ||
        fill
      }
    />
    <path
      d="M7.8497 8.20387C7.8497 8.10568 7.93566 8.01984 8.03367 8.01984H9.13779C9.23591 8.01984 9.32182 8.10568 9.32182 8.20387V9.06255C9.32182 9.16073 9.23585 9.24657 9.13779 9.24657H8.03367C7.9356 9.24657 7.8497 9.16073 7.8497 9.06255V8.20387Z"
      fill={
        (fill === main && light) ||
        (wrapperColor && wrapperColor !== main && wrapperColor) ||
        (fill === light && main) ||
        fill
      }
    />
  </svg>
)
