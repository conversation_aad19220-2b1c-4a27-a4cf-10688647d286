import React from 'react'

export const frangipani = (props) => (
  <svg
    width={props.width || '606'}
    height={props.height || '598'}
    viewBox="0 0 606 598"
    preserveAspectRatio="xMidYMid meet"
  >
    <g
      transform="translate(0.000000,598.000000) scale(0.100000,-0.100000)"
      fill={props.fill || '#000000'}
      stroke="none"
    >
      <path
        d="M2535 5944 c-118 -25 -196 -68 -273 -148 -37 -39 -91 -107 -120 -151
-73 -111 -186 -342 -219 -450 -27 -88 -28 -96 -27 -325 0 -231 1 -238 37 -410
41 -199 92 -390 178 -667 32 -105 59 -193 59 -196 0 -3 -35 8 -77 25 -736 285
-991 364 -1283 400 -96 12 -233 21 -305 22 -157 0 -215 -18 -298 -94 -87 -80
-143 -234 -153 -425 -10 -177 24 -279 165 -490 199 -296 358 -462 517 -537
109 -51 449 -140 802 -209 l114 -22 -34 -26 c-216 -168 -524 -488 -534 -555
-2 -16 -51 -93 -77 -122 -10 -12 -19 -28 -21 -35 -1 -8 -4 -18 -8 -24 -110
-180 -169 -435 -148 -640 30 -291 255 -594 500 -674 219 -72 605 37 956 270
201 134 415 378 599 684 l69 117 15 -34 c158 -363 293 -567 468 -708 222 -179
481 -309 753 -378 97 -24 121 -26 310 -26 189 0 213 2 305 27 283 75 468 234
547 472 20 62 23 89 23 240 0 153 -3 181 -28 275 -50 196 -126 362 -248 546
-85 128 -186 231 -412 419 -87 72 -157 132 -157 135 0 4 17 12 38 19 178 63
522 232 677 333 406 263 625 618 710 1150 20 127 19 184 -5 289 -38 173 -124
306 -237 367 -189 100 -407 139 -728 128 -319 -10 -445 -43 -658 -170 -183
-110 -459 -328 -602 -475 -27 -28 -53 -51 -57 -51 -4 0 -9 15 -12 33 -3 17
-17 102 -31 187 -15 85 -30 180 -35 210 -5 30 -11 66 -14 80 -3 14 -11 52 -16
85 -40 223 -71 346 -138 545 -130 382 -240 634 -327 748 -54 72 -169 172 -230
202 -23 11 -62 20 -87 20 -25 0 -62 7 -81 15 -44 18 -71 18 -162 -1z m395
-3388 c0 -55 13 -91 36 -100 20 -8 64 19 64 39 0 18 5 18 31 5 17 -10 14 -15
-36 -66 -55 -54 -55 -55 -38 -78 10 -13 33 -33 51 -45 32 -21 33 -25 27 -64
-11 -64 -20 -64 -43 0 -15 41 -30 63 -51 76 -60 35 -91 17 -85 -50 4 -55 -13
-73 -71 -73 -76 0 -105 55 -35 66 46 7 64 30 73 90 7 40 5 42 -27 58 -19 9
-48 16 -65 16 -20 0 -31 5 -31 14 0 23 20 29 38 13 29 -25 72 -30 94 -11 16
15 18 26 12 70 -5 46 -4 53 17 68 34 24 39 20 39 -28z"
      />
    </g>
  </svg>
)
