import React from 'react'

export const items = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      primary: { darker, main, lightest },
    },
  },
  wrapperColor,
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      d="M7.30098 11.9745H1.46019C0.922527 11.9745 0.486816 12.4102 0.486816 12.9478V18.7886C0.486816 19.3263 0.922527 19.762 1.46019 19.762H7.30098C7.83846 19.762 8.27436 19.3263 8.27436 18.7886V12.9478C8.27436 12.4102 7.83846 11.9745 7.30098 11.9745ZM7.30098 2.23999H1.46019C0.922527 2.23999 0.486816 2.6757 0.486816 3.21336V9.05416C0.486816 9.59182 0.922527 10.0275 1.46019 10.0275H7.30098C7.83846 10.0275 8.27436 9.59182 8.27436 9.05416V3.21336C8.27436 2.6757 7.83846 2.23999 7.30098 2.23999Z"
      fill={(wrapperColor && wrapperColor !== main && 'transparent') || main || fill}
    />
    <path
      d="M21.9999 15.625V16.1117C21.9999 16.246 21.8909 16.355 21.7566 16.355H10.4645C10.3302 16.355 10.2212 16.246 10.2212 16.1117V15.625C10.2212 15.4905 10.3302 15.3814 10.4645 15.3814H21.7566C21.8909 15.3814 21.9999 15.4905 21.9999 15.625ZM10.2212 4.43026V3.94357C10.2212 3.80906 10.3302 3.70004 10.4645 3.70004H19.2256C19.36 3.70004 19.469 3.80906 19.469 3.94357V4.43026C19.469 4.56458 19.36 4.6736 19.2256 4.6736H10.4645C10.3302 4.6736 10.2212 4.56458 10.2212 4.43026ZM10.4645 6.62053H20.199C20.3333 6.62053 20.4423 6.51151 20.4423 6.37719V5.89032C20.4423 5.756 20.3333 5.64697 20.199 5.64697H10.4645C10.3302 5.64697 10.2212 5.756 10.2212 5.89032V6.37719C10.2212 6.51151 10.3302 6.62053 10.4645 6.62053ZM10.4645 8.56728H16.3051C16.4397 8.56728 16.5485 8.45844 16.5485 8.32394V7.83725C16.5485 7.70293 16.4397 7.59391 16.3051 7.59391H10.4645C10.3302 7.59391 10.2212 7.70293 10.2212 7.83725V8.32394C10.2212 8.45844 10.3302 8.56728 10.4645 8.56728ZM10.4645 14.4081H19.2256C19.36 14.4081 19.469 14.2991 19.469 14.1647V13.678C19.469 13.5435 19.36 13.4347 19.2256 13.4347H10.4645C10.3302 13.4347 10.2212 13.5435 10.2212 13.678V14.1647C10.2212 14.2991 10.3302 14.4081 10.4645 14.4081ZM16.3051 17.3284H10.4645C10.3302 17.3284 10.2212 17.4374 10.2212 17.5717V18.0586C10.2212 18.1929 10.3302 18.3019 10.4645 18.3019H16.3051C16.4397 18.3019 16.5485 18.1929 16.5485 18.0586V17.5717C16.5485 17.4374 16.4397 17.3284 16.3051 17.3284Z"
      fill={
        (fill === main && darker) ||
        (wrapperColor && wrapperColor !== main && `${light}A8`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <path
      d="M8.59008 2.48632L9.135 3.03124C9.28551 3.18175 9.28551 3.42584 9.135 3.57635L3.85326 8.8579C3.55243 9.15891 3.06444 9.15891 2.76342 8.8579L0.112881 6.20735C-0.037627 6.05685 -0.037627 5.81294 0.112881 5.66244L0.657799 5.11752C0.808307 4.96701 1.05221 4.96701 1.20272 5.11752L3.03598 6.95059C3.18648 7.1011 3.43039 7.1011 3.58089 6.95059L8.04516 2.48632C8.19548 2.33582 8.43957 2.33582 8.59008 2.48632ZM8.04516 12.221L3.58089 16.6852C3.43039 16.8358 3.1863 16.8358 3.03579 16.6852L1.20272 14.852C1.05221 14.7015 0.808307 14.7015 0.657799 14.852L0.112881 15.3969C-0.037627 15.5474 -0.037627 15.7915 0.112881 15.9418L2.76342 18.5924C3.06425 18.8934 3.55224 18.8934 3.85326 18.5924L9.135 13.3106C9.28551 13.1603 9.28551 12.9162 9.135 12.7657L8.59008 12.2208C8.43957 12.0705 8.19548 12.0705 8.04516 12.221Z"
      fill={light}
    />
  </svg>
)
