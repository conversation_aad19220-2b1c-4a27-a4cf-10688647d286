import React from 'react'

export const dashboard = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M14.0002 8.4H15.6002C17.2002 8.4 18.0002 7.6 18.0002 6V4.4C18.0002 2.8 17.2002 2 15.6002 2H14.0002C12.4002 2 11.6002 2.8 11.6002 4.4V6C11.6002 7.6 12.4002 8.4 14.0002 8.4Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <path
      d="M4.40017 18H6.00017C7.60017 18 8.40017 17.2 8.40017 15.6V14C8.40017 12.4 7.60017 11.6 6.00017 11.6H4.40017C2.80017 11.6 2.00017 12.4 2.00017 14V15.6C2.00017 17.2 2.80017 18 4.40017 18Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <path
      d="M5.20017 8.4C6.96748 8.4 8.40017 6.96731 8.40017 5.2C8.40017 3.43269 6.96748 2 5.20017 2C3.43286 2 2.00017 3.43269 2.00017 5.2C2.00017 6.96731 3.43286 8.4 5.20017 8.4Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <path
      d="M14.8002 18C16.5675 18 18.0002 16.5673 18.0002 14.8C18.0002 13.0327 16.5675 11.6 14.8002 11.6C13.0329 11.6 11.6002 13.0327 11.6002 14.8C11.6002 16.5673 13.0329 18 14.8002 18Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
