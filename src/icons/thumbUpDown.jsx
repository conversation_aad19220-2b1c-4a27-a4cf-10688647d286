import React from 'react'

export const thumbUpDown = ({ fill, width, height, down = false }) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox="0 0 16 16"
    style={{ transform: down ? 'rotate(180deg)' : 'initial' }}
  >
    <path
      d="M3.25 7H0.750001C0.335781 7 0 7.33579 0 7.75001V15.25C0 15.6642 0.335781 16 0.750001 16H3.25C3.66422 16 4 15.6642 4 15.25V7.75001C4 7.33579 3.66422 7 3.25 7ZM2 14.75C1.58578 14.75 1.25 14.4142 1.25 14C1.25 13.5858 1.58578 13.25 2 13.25C2.41422 13.25 2.75 13.5858 2.75 14C2.75 14.4142 2.41422 14.75 2 14.75ZM12 2.54538C12 3.87088 11.1884 4.61438 10.9601 5.5H14.1389C15.1826 5.5 15.9951 6.36707 16 7.31557C16.0026 7.87613 15.7642 8.4796 15.3925 8.85298L15.3891 8.85641C15.6964 9.58569 15.6465 10.6076 15.0982 11.3398C15.3695 12.149 15.096 13.1431 14.5863 13.676C14.7206 14.2259 14.6564 14.6939 14.3941 15.0707C13.7563 15.9871 12.1755 16 10.8387 16L10.7498 16C9.24085 15.9994 8.00588 15.45 7.01357 15.0086C6.51491 14.7867 5.86291 14.5121 5.36822 14.503C5.16385 14.4993 5 14.3325 5 14.1281V7.44776C5 7.34776 5.04007 7.25179 5.11119 7.18147C6.34913 5.95822 6.88144 4.66313 7.8961 3.64675C8.35872 3.18325 8.52697 2.48313 8.68963 1.80606C8.8286 1.22791 9.11929 0 9.75001 0C10.5 0 12 0.25 12 2.54538Z"
      fill={fill || '#8ED63B'}
    />
  </svg>
)
