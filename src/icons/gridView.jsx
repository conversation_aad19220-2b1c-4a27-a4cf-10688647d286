import React from 'react'

export const gridView = ({ width = 20, height = 20, fill, stroke, strokeWidth }) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill={fill || 'none'}>
    <path
      d="M2 3C2 2.44772 2.44772 2 3 2H5C5.55228 2 6 2.44772 6 3V5C6 5.55228 5.55228 6 5 6H3C2.44772 6 2 5.55228 2 5V3Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M8 3C8 2.44772 8.44772 2 9 2H11C11.5523 2 12 2.44772 12 3V5C12 5.55228 11.5523 6 11 6H9C8.44772 6 8 5.55228 8 5V3Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M14 3C14 2.44772 14.4477 2 15 2H17C17.5523 2 18 2.44772 18 3V5C18 5.55228 17.5523 6 17 6H15C14.4477 6 14 5.55228 14 5V3Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M2 9C2 8.44772 2.44772 8 3 8H5C5.55228 8 6 8.44772 6 9V11C6 11.5523 5.55228 12 5 12H3C2.44772 12 2 11.5523 2 11V9Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M8 9C8 8.44772 8.44772 8 9 8H11C11.5523 8 12 8.44772 12 9V11C12 11.5523 11.5523 12 11 12H9C8.44772 12 8 11.5523 8 11V9Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M14 9C14 8.44772 14.4477 8 15 8H17C17.5523 8 18 8.44772 18 9V11C18 11.5523 17.5523 12 17 12H15C14.4477 12 14 11.5523 14 11V9Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M2 15C2 14.4477 2.44772 14 3 14H5C5.55228 14 6 14.4477 6 15V17C6 17.5523 5.55228 18 5 18H3C2.44772 18 2 17.5523 2 17V15Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M8 15C8 14.4477 8.44772 14 9 14H11C11.5523 14 12 14.4477 12 15V17C12 17.5523 11.5523 18 11 18H9C8.44772 18 8 17.5523 8 17V15Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
    <path
      d="M14 15C14 14.4477 14.4477 14 15 14H17C17.5523 14 18 14.4477 18 15V17C18 17.5523 17.5523 18 17 18H15C14.4477 18 14 17.5523 14 17V15Z"
      stroke={stroke || fill || '#E6E6E6'}
      strokeWidth={strokeWidth || 2}
    />
  </svg>
)
