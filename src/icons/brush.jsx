import React from 'react'

export const brush = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      d="M20.8358 9.09025C21.0546 8.87141 21.0546 8.51652 20.8358 8.29764L13.7023 1.16416C13.4834 0.94528 13.1286 0.94528 12.9097 1.16416L10.537 3.53692L14.5 7.5L18.463 11.4631L20.8358 9.09025Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 2}
      fill="transparent"
    />
    <path
      d="M6.73973 20.0142C7.37548 19.3784 7.72562 18.5343 7.72559 17.6373C7.72559 16.9653 8.81744 15.6128 9.93399 15.1503C10.2569 15.0165 10.6972 14.9124 10.9608 15.176C11.2375 15.4526 11.267 15.4822 11.267 15.4822L11.2702 15.4855C11.6924 15.9076 12.2554 16.1401 12.8555 16.1401C13.4556 16.14 14.0186 15.9075 14.4407 15.4854L16.3996 13.5266L8.4735 5.60043L6.51461 7.55932C6.09247 7.98146 5.85996 8.54443 5.85996 9.14461C5.85996 9.74472 6.09244 10.3077 6.51514 10.7304C6.51671 10.732 6.53875 10.754 6.82414 11.0393C6.93608 11.1513 6.98828 11.2887 6.98828 11.4717C6.98828 12.5712 5.24401 14.2745 4.36281 14.2745C2.50865 14.2744 1.00007 15.783 1 17.6372C1.00004 18.5343 1.35018 19.3785 1.98593 20.0141C2.62164 20.6499 3.46577 21 4.36281 21C5.25985 21 6.10402 20.6499 6.73973 20.0142Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 2}
      fill="transparent"
    />
    <path
      d="M4.3999 16.1C3.5715 16.1 2.8999 16.7716 2.8999 17.6C2.8999 18.4285 3.5715 19.1 4.3999 19.1C5.2283 19.1 5.8999 18.4284 5.8999 17.6C5.8999 16.7716 5.2283 16.1 4.3999 16.1Z"
      fill={fill || stroke || dark}
    />
  </svg>
)
