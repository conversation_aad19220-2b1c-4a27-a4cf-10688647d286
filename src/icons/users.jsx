import React from 'react'

export const users = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M13.3545 17.1421L13.3548 14.464C13.3549 12.9847 12.1557 11.7854 10.6764 11.7854H4.67868C3.19957 11.7854 2.00047 12.9843 2.0003 14.4634L2 17.1421M17.9998 17.1423L18 14.4641C18.0001 12.9848 16.8009 11.7855 15.3216 11.7855M12.8386 3.38359C13.4964 3.87163 13.9226 4.65401 13.9226 5.53595C13.9226 6.41789 13.4964 7.20027 12.8386 7.6883M10.4115 5.5358C10.4115 7.01503 9.21235 8.21418 7.73312 8.21418C6.25389 8.21418 5.05474 7.01503 5.05474 5.5358C5.05474 4.05657 6.25389 2.85742 7.73312 2.85742C9.21235 2.85742 10.4115 4.05657 10.4115 5.5358Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
