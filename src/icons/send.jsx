import React from 'react'

export const send = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M20.4272 10.0001L9.89435 10.0001M5.45584 13.4745H3.42725M5.45584 10.1221H1.42725M5.45584 6.76976H3.42725M9.01674 3.83009L20.0628 9.18578C20.7431 9.51559 20.7431 10.4847 20.0628 10.8145L9.01674 16.1702C8.26001 16.5371 7.45567 15.7635 7.79276 14.9931L9.81847 10.3629C9.91964 10.1316 9.91964 9.86863 9.81847 9.63738L7.79276 5.00719C7.45568 4.23671 8.26001 3.46319 9.01674 3.83009Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
