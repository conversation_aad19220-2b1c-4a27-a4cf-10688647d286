import React from 'react'

export const chart = ({ fill, width, height }) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.775 7C21.9242 7.65461 22 8.32542 22 9H13V0C13.6746 0 14.3454 0.0758356 15 0.225036C15.4923 0.337242 15.9754 0.490942 16.4442 0.685084C17.5361 1.13738 18.5282 1.80031 19.364 2.63604C20.1997 3.47177 20.8626 4.46392 21.3149 5.55585C21.5091 6.02455 21.6628 6.5077 21.775 7ZM19.7082 7C19.6397 6.77018 19.5593 6.54361 19.4672 6.32122C19.1154 5.47194 18.5998 4.70026 17.9497 4.05025C17.2997 3.40024 16.5281 2.88463 15.6788 2.53284C15.4564 2.44073 15.2298 2.36031 15 2.2918V7H19.7082Z"
      fill={fill || 'white'}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 13C0 8.02944 4.02944 4 9 4C9.67458 4 10.3454 4.07584 11 4.22504V11H17.775C17.9242 11.6546 18 12.3254 18 13C18 17.9706 13.9706 22 9 22C4.02944 22 0 17.9706 0 13ZM15.8035 13H9V6.19648C5.24252 6.19648 2.19648 9.24252 2.19648 13C2.19648 16.7575 5.24252 19.8035 9 19.8035C12.7575 19.8035 15.8035 16.7575 15.8035 13Z"
      fill={fill || 'white'}
    />
  </svg>
)
