import React from 'react'

export const arrowLeft = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M9.02761 15L4.1665 10M4.1665 10L9.02761 5M4.1665 10H15.8332"
      stroke={stroke || fill || dark}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={strokeWidth || 2}
    />
  </svg>
)
