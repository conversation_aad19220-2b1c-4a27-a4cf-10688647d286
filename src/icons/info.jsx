import React from 'react'

export const info = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg width={width || 16} height={height || 17} viewBox="0 0 16 17" fill="none">
    <path
      d="M8.00001 8.49998L8.00001 11.7M8.00001 6.1281V6.09998M1.60001 8.49997C1.60001 4.96535 4.46538 2.09998 8.00001 2.09998C11.5346 2.09998 14.4 4.96535 14.4 8.49998C14.4 12.0346 11.5346 14.9 8.00001 14.9C4.46538 14.9 1.60001 12.0346 1.60001 8.49997Z"
      stroke={fill || stroke || light}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
