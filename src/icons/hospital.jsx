import React from 'react'

export const hospital = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      status: { error },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M2 7.70316C2 7.3748 2.16713 7.06676 2.44851 6.87647L9.38184 2.18771C9.75193 1.93743 10.2481 1.93743 10.6182 2.18771L17.5515 6.87647C17.8329 7.06676 18 7.3748 18 7.70316V16.4784C18 17.3188 17.2837 18 16.4 18H3.6C2.71634 18 2 17.3188 2 16.4784V7.70316Z"
      fill={fill || error}
      stroke={stroke || error}
      strokeWidth={strokeWidth || 1.4}
    />
    <path d="M10 7L10 15M14 11L6 11" stroke="white" strokeWidth="1.4" strokeLinecap="round" />
  </svg>
)
