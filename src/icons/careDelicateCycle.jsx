import React from 'react'

export const careDelicateCycle = ({
  width,
  height,
  fill,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.0736 8.75717C19.4185 7.51909 19.8651 5.91609 20.0734 4.71467L20.065 4.70801C19.9462 4.64645 19.8269 4.58183 19.7061 4.51641C18.6139 3.92483 17.4 3.26737 15.331 4.20812C15.0133 4.35258 14.7315 4.55603 14.4317 4.77257C13.6731 5.32033 12.7985 5.95189 10.9319 5.92442C9.30223 5.90018 8.38928 5.30527 7.52774 4.74386C6.55347 4.109 5.64496 3.51698 3.84006 3.84152C1.63276 4.23816 1.7895 4.7721 2.58537 7.48326C2.76582 8.09798 2.97913 8.82463 3.20519 9.68698C3.35756 10.2678 3.46951 10.7418 3.56081 11.1284C3.78392 12.0731 3.88372 12.4957 4.14877 12.678C4.36325 12.8256 4.68596 12.8159 5.26979 12.7983C5.5031 12.7913 5.77812 12.783 6.10459 12.783H12.14C13.5064 12.783 14.5493 12.8203 15.3521 12.8491H15.3521C16.8679 12.9034 17.5274 12.927 17.8913 12.6106C18.1716 12.3669 18.2765 11.9216 18.4623 11.1333C18.4882 11.0234 18.5156 10.9069 18.5453 10.7834C18.722 10.0619 18.8786 9.47036 19.0736 8.75717ZM14.1616 14.0532H14.1615H14.1615C10.3075 14.0854 5.77364 14.1232 3.18019 14.0044L3.1752 13.9961C2.75301 13.2527 1.82985 9.49924 1.13836 6.68769C0.871892 5.60424 0.639826 4.66067 0.484087 4.08314C0.422188 3.85294 0.361665 3.63876 0.305533 3.44011C-0.100723 2.0024 -0.276922 1.37885 0.920662 1.39203C1.12585 1.69207 1.26851 2.16119 1.40076 2.59607L1.40076 2.59607L1.40076 2.59607C1.48223 2.86399 1.55976 3.11892 1.64551 3.3133C1.81111 3.25409 1.97277 3.19449 2.13165 3.13591C3.55548 2.61095 4.75648 2.16815 6.57782 2.82173C7.14356 3.025 7.62302 3.33853 8.09942 3.65005C8.88449 4.16343 9.66122 4.67135 10.802 4.67135C12.3665 4.67135 12.6887 4.45269 13.7065 3.762L13.7067 3.76187L13.7074 3.76139C13.8026 3.69677 13.9039 3.62801 14.013 3.55491C15.5084 2.55361 16.2792 2.55408 17.8909 2.55505L18.0471 2.55512C18.4394 2.55512 18.9896 2.76843 19.4684 2.95407C19.8641 3.10748 20.2111 3.242 20.38 3.22165C20.551 2.98555 20.6326 2.66528 20.7104 2.35989C20.8138 1.95402 20.9105 1.57444 21.2015 1.45368C22.1113 1.07709 22.073 2.14354 21.9063 2.72342C21.7447 3.30928 21.6026 3.89245 21.4593 4.48049L21.4592 4.48057L21.4592 4.48065L21.4592 4.48078L21.4591 4.48115C21.2833 5.20246 21.1057 5.93108 20.8882 6.68093C20.5316 7.91234 20.1567 9.40704 19.8751 10.6118C19.7001 11.355 19.5535 11.9232 19.3518 12.6547C19.3009 12.8403 19.2698 13.0107 19.242 13.1636C19.1433 13.7054 19.0846 14.0277 18.3204 14.0277C17.1593 14.0282 15.7159 14.0403 14.1619 14.0532L14.1616 14.0532ZM4.23498 18.5412C3.61344 18.5412 3.43848 18.2613 3.44681 17.6647L3.45014 17.6664C3.95577 17.2395 4.22776 17.2538 4.76307 17.2821H4.76308H4.76309C4.91933 17.2904 5.09798 17.2998 5.31142 17.2998H16.3091C16.4458 17.2994 16.6095 17.2887 16.7853 17.2771L16.7854 17.2771C17.3749 17.2383 18.1008 17.1906 18.4037 17.4948C18.9486 18.0413 18.4237 18.5412 17.6505 18.5412H4.23498ZM3.44681 19.831C3.43848 20.4275 3.61344 20.7075 4.23498 20.7075H17.6505C18.4237 20.7075 18.9486 20.2076 18.4037 19.661C18.1008 19.3568 17.3748 19.4046 16.7854 19.4433C16.6095 19.4549 16.4458 19.4657 16.3091 19.466H5.31142C5.09797 19.466 4.91931 19.4566 4.76307 19.4483C4.22776 19.4201 3.95577 19.4057 3.45014 19.8326L3.44681 19.831ZM3.995 16.3304C3.31347 16.1738 3.17017 15.6322 3.8367 15.2557C4.13497 15.089 17.6238 15.0657 18.0471 15.1573C18.7286 15.304 18.8419 15.9672 18.2287 16.2671L3.995 16.3304Z"
      fill={fill || main}
    />
  </svg>
)
