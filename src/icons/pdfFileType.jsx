import React from 'react'

export const pdfFileType = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
}) => (
  <svg width={width || 16} height={height || 20} viewBox="0 0 16 20" fill="none">
    <g>
      <path
        d="M3.12517 0H9.81183L15.479 5.90705V17.3963C15.479 18.8356 14.3146 20 12.8803 20H3.12517C1.6859 20 0.521484 18.8356 0.521484 17.3963V2.60369C0.521459 1.16441 1.68587 0 3.12517 0Z"
        fill={fill || '#DE350B'}
      />
      <path
        d="M0.84494 2.60369V2.60368C0.844918 1.34306 1.86451 0.323455 3.12517 0.323455H9.6739L15.1555 6.03712V17.3963C15.1555 18.6573 14.1355 19.6765 12.8803 19.6765H3.12517C1.86454 19.6765 0.84494 18.6569 0.84494 17.3963V2.60369Z"
        stroke={stroke || '#172B4D'}
        strokeOpacity={strokeOpacity || '0.1'}
        strokeWidth={strokeWidth || '0.647'}
      />
      <g opacity="0.3">
        <path d="M9.80664 0V5.86207H15.4788L9.80664 0Z" fill={fillCorner || 'white'} />
      </g>
      <g>
        <path
          d="M3.41455 14.9225V11.2693H4.96878C5.35359 11.2693 5.65845 11.3743 5.88833 11.5892C6.11821 11.7991 6.23316 12.0839 6.23316 12.4388C6.23316 12.7936 6.11821 13.0784 5.88833 13.2883C5.65845 13.5032 5.35359 13.6082 4.96878 13.6082H4.34908V14.9225H3.41455ZM4.34908 12.8136H4.86383C5.00375 12.8136 5.1137 12.7836 5.18868 12.7137C5.26363 12.6487 5.30363 12.5587 5.30363 12.4388C5.30363 12.3189 5.26365 12.2289 5.18868 12.1639C5.11373 12.094 5.00378 12.064 4.86383 12.064H4.34908V12.8136ZM6.61795 14.9225V11.2693H7.9123C8.16717 11.2693 8.40706 11.3043 8.63194 11.3793C8.85681 11.4542 9.06173 11.5592 9.24162 11.7041C9.42155 11.8441 9.56647 12.034 9.67141 12.2739C9.77136 12.5137 9.82634 12.7886 9.82634 13.0984C9.82634 13.4033 9.77138 13.6782 9.67141 13.918C9.56647 14.1579 9.42155 14.3478 9.24162 14.4877C9.0617 14.6327 8.85681 14.7376 8.63194 14.8126C8.40706 14.8875 8.16717 14.9225 7.9123 14.9225H6.61795ZM7.53249 14.1279H7.80235C7.94727 14.1279 8.08221 14.113 8.20715 14.078C8.32708 14.043 8.44203 13.988 8.55198 13.913C8.65693 13.8381 8.74188 13.7331 8.80185 13.5932C8.86182 13.4533 8.89181 13.2883 8.89181 13.0984C8.89181 12.9035 8.86182 12.7386 8.80185 12.5987C8.74188 12.4588 8.65693 12.3538 8.55198 12.2789C8.44203 12.2039 8.3271 12.1489 8.20715 12.1139C8.08221 12.079 7.94727 12.064 7.80235 12.064H7.53249V14.1279ZM10.2961 14.9225V11.2693H12.8948V12.064H11.2306V12.6487H12.56V13.4383H11.2306V14.9225H10.2961Z"
          fill={colorText || 'white'}
        />
      </g>
    </g>
  </svg>
)
