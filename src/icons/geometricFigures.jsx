import React from 'react'

export const geometricFigures = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      primary: { darker, main, lightest },
    },
  },
  wrapperColor,
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      d="M21 12.6694C21 12.3933 20.7761 12.1694 20.5 12.1694H12.6694C12.3933 12.1694 12.1694 12.3933 12.1694 12.6694V20.5C12.1694 20.7761 12.3933 21 12.6694 21H20.5C20.7761 21 21 20.7761 21 20.5V12.6694Z"
      fill={(fill === main && main) || (fill === light && light) || fill}
    />
    <path
      d="M20.4912 3.6297C19.705 2.07132 18.0894 1 16.228 1C13.5968 1 11.4563 3.14066 11.4563 5.77188C11.4563 8.40311 13.5968 10.5436 16.228 10.5436C18.0894 10.5436 19.705 9.47229 20.4912 7.91391C20.8163 7.26938 20.9999 6.54169 20.9999 5.77173C20.9999 5.00192 20.8163 4.27423 20.4912 3.6297Z"
      fill={
        (fill === main && darker) ||
        (wrapperColor && wrapperColor !== main && `${light}D9`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <path
      d="M6.56571 1.76569C6.37408 1.42821 5.88776 1.42821 5.69612 1.76568L1.42413 9.28875C1.23485 9.62207 1.4756 10.0356 1.85892 10.0356H10.4028C10.7861 10.0356 11.0268 9.62207 10.8376 9.28875L6.56571 1.76569Z"
      fill={(fill === main && main) || (fill === light && light) || fill}
    />
    <path
      d="M1.14433 16.4437C1.05502 16.5984 1.05502 16.789 1.14434 16.9437L3.34193 20.75C3.43125 20.9047 3.59631 21 3.77494 21H8.17013C8.34876 21 8.51382 20.9047 8.60314 20.75L10.8007 16.9437C10.89 16.789 10.89 16.5984 10.8007 16.4437L8.60314 12.6372C8.51382 12.4825 8.34876 12.3872 8.17012 12.3872H3.77495C3.59631 12.3872 3.43125 12.4825 3.34193 12.6372L1.14433 16.4437Z"
      fill={
        (fill === main && darker) ||
        (wrapperColor && wrapperColor !== main && `${light}D9`) ||
        (fill === light && lightest) ||
        fill
      }
    />
  </svg>
)
