import React from 'react'

export const target = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M18.2796 10.5C18.2796 10.1134 17.9662 9.8 17.5796 9.8C17.193 9.8 16.8796 10.1134 16.8796 10.5H18.2796ZM9.57959 3.2C9.96619 3.2 10.2796 2.8866 10.2796 2.5C10.2796 2.1134 9.96619 1.8 9.57959 1.8V3.2ZM14.2796 10.5C14.2796 10.1134 13.9662 9.8 13.5796 9.8C13.193 9.8 12.8796 10.1134 12.8796 10.5H14.2796ZM9.57959 7.2C9.96619 7.2 10.2796 6.8866 10.2796 6.5C10.2796 6.1134 9.96619 5.8 9.57959 5.8V7.2ZM13.6101 7.2982C13.8835 7.02483 13.8835 6.58162 13.6101 6.30825C13.3367 6.03488 12.8935 6.03488 12.6201 6.30825L13.6101 7.2982ZM9.08461 9.84378C8.81125 10.1171 8.81125 10.5604 9.08461 10.8337C9.35798 11.1071 9.8012 11.1071 10.0746 10.8337L9.08461 9.84378ZM18.4201 4.32843L18.9151 4.8234C19.1026 4.63592 19.168 4.3586 19.0842 4.10707C19.0003 3.85553 18.7816 3.67296 18.5191 3.63546L18.4201 4.32843ZM15.2381 7.51041L15.0168 8.17449C15.2683 8.25833 15.5456 8.19287 15.7331 8.00538L15.2381 7.51041ZM12.4097 4.68198L11.9147 4.18701C11.7272 4.37449 11.6618 4.65181 11.7456 4.90334L12.4097 4.68198ZM15.5917 1.5L16.2846 1.401C16.2472 1.13853 16.0646 0.919766 15.813 0.835922C15.5615 0.752077 15.2842 0.817543 15.0967 1.00503L15.5917 1.5ZM13.1168 6.8033L12.4527 7.02466C12.5224 7.23368 12.6864 7.39771 12.8955 7.46738L13.1168 6.8033ZM15.9452 3.97487L15.2523 4.07387C15.2963 4.38185 15.5383 4.62384 15.8462 4.66784L15.9452 3.97487ZM16.8796 10.5C16.8796 14.5317 13.6113 17.8 9.57959 17.8V19.2C14.3845 19.2 18.2796 15.3049 18.2796 10.5H16.8796ZM9.57959 17.8C5.54791 17.8 2.27959 14.5317 2.27959 10.5H0.87959C0.87959 15.3049 4.77471 19.2 9.57959 19.2V17.8ZM2.27959 10.5C2.27959 6.46832 5.54791 3.2 9.57959 3.2V1.8C4.77471 1.8 0.87959 5.69512 0.87959 10.5H2.27959ZM12.8796 10.5C12.8796 12.3225 11.4021 13.8 9.57959 13.8V15.2C12.1753 15.2 14.2796 13.0957 14.2796 10.5H12.8796ZM9.57959 13.8C7.75705 13.8 6.27959 12.3225 6.27959 10.5H4.87959C4.87959 13.0957 6.98385 15.2 9.57959 15.2V13.8ZM6.27959 10.5C6.27959 8.67746 7.75705 7.2 9.57959 7.2V5.8C6.98385 5.8 4.87959 7.90426 4.87959 10.5H6.27959ZM12.6201 6.30825L9.08461 9.84378L10.0746 10.8337L13.6101 7.2982L12.6201 6.30825ZM17.9251 3.83345L14.7432 7.01543L15.7331 8.00538L18.9151 4.8234L17.9251 3.83345ZM12.9047 5.17696L16.0867 1.99497L15.0967 1.00503L11.9147 4.18701L12.9047 5.17696ZM15.4595 6.84633L13.3382 6.13922L12.8955 7.46738L15.0168 8.17449L15.4595 6.84633ZM13.7809 6.58194L13.0738 4.46062L11.7456 4.90334L12.4527 7.02466L13.7809 6.58194ZM14.8987 1.599L15.2523 4.07387L16.6382 3.87588L16.2846 1.401L14.8987 1.599ZM15.8462 4.66784L18.3211 5.02139L18.5191 3.63546L16.0442 3.28191L15.8462 4.66784Z"
      fill={fill || dark}
    />
  </svg>
)
