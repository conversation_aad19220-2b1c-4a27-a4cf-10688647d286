import React from 'react'

export const signal = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark, light },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <rect width="20" height="20" rx="5" fill="#3A76F0" />
    <rect x="0.5" y="0.5" width="19" height="19" rx="4.5" stroke="#172B4D" strokeOpacity="0.1" />
    <path
      d="M8.5 3.93262L8.64063 4.50098C8.0874 4.63696 7.55811 4.85596 7.07031 5.15039L6.76953 4.64844C7.30689 4.32397 7.89038 4.08252 8.5 3.93262ZM11.3594 4.50098C11.9126 4.63696 12.4419 4.85596 12.9297 5.15039L13.2324 4.64844C12.6943 4.32373 12.1104 4.08252 11.5 3.93262L11.3594 4.50098ZM4.64844 6.76929C4.32397 7.30664 4.08252 7.89014 3.93262 8.49951L4.50098 8.64014C4.63696 8.08716 4.85596 7.55762 5.15039 7.07007L4.64844 6.76929ZM4.39941 9.15088L3.82031 9.06299C3.72656 9.68384 3.72656 10.3152 3.82031 10.936L4.39941 10.8481C4.3147 10.2869 4.3147 9.71216 4.39941 9.15088ZM12.9297 14.8486C12.4426 15.1428 11.9138 15.3618 11.3613 15.4981L11.502 16.0664C12.1108 15.9163 12.6936 15.6751 13.2305 15.3506L12.9297 14.8486ZM15.6006 10.8481L16.1797 10.936C16.2734 10.3152 16.2734 9.68384 16.1797 9.06299L15.6006 9.15088C15.6853 9.71216 15.6853 10.2869 15.6006 10.8481ZM15.499 11.3589C15.363 11.9119 15.144 12.4414 14.8496 12.929L15.3516 13.2317C15.6763 12.6936 15.9175 12.1096 16.0674 11.4995L15.499 11.3589ZM10.8486 15.6006C10.2861 15.6853 9.71387 15.6853 9.15137 15.6006L9.06348 16.1797C9.68433 16.2734 10.3157 16.2734 10.9365 16.1797L10.8486 15.6006ZM14.5596 13.3596C14.2217 13.8171 13.8171 14.2212 13.3594 14.5586L13.707 15.0303C14.2122 14.6589 14.6585 14.2141 15.0313 13.7102L14.5596 13.3596ZM13.3594 5.44043C13.8171 5.77808 14.2219 6.18262 14.5596 6.64038L15.0313 6.28882C14.6597 5.78516 14.2146 5.34033 13.7109 4.96875L13.3594 5.44043ZM5.44043 6.64038C5.77808 6.18262 6.18286 5.77808 6.64063 5.44043L6.28906 4.96875C5.7854 5.34033 5.34033 5.78516 4.96875 6.28882L5.44043 6.64038ZM14.8496 7.07007C15.1438 7.55713 15.3628 8.08594 15.499 8.63819L16.0674 8.49756C15.9172 7.88892 15.6758 7.30615 15.3516 6.76929L14.8496 7.07007ZM9.15137 4.39941C9.71387 4.3147 10.2861 4.3147 10.8486 4.39941L10.9365 3.82031C10.3157 3.72656 9.68433 3.72656 9.06348 3.82031L9.15137 4.39941ZM5.74121 15.1856L4.53125 15.4678L4.81348 14.2581L4.24316 14.1243L3.96094 15.334C3.86206 15.7564 4.24268 16.137 4.66504 16.0381L5.87402 15.7607L5.74121 15.1856ZM4.36523 13.6018L4.93555 13.7346L5.13086 12.8958C4.84619 12.4172 4.63403 11.8994 4.50098 11.3589L3.93262 11.4995C4.06006 12.0171 4.25366 12.5164 4.50879 12.9846L4.36523 13.6018ZM6.26074 15.0664L6.39356 15.6367L7.01074 15.4932C7.47901 15.7483 7.97827 15.9419 8.4961 16.0693L8.63672 15.501C8.09717 15.3665 7.58057 15.1528 7.10352 14.8672L6.26074 15.0664ZM5.55737 7.54468C4.66406 9.16211 4.71997 11.137 5.70313 12.7014L5.21484 14.7842L7.29785 14.2961C9.125 15.4468 11.4797 15.3162 13.1687 13.9707C14.8574 12.6255 15.5105 10.3594 14.7971 8.32154C13.3496 4.18823 7.66187 3.73437 5.55737 7.54468Z"
      fill={light || fill}
    />
  </svg>
)
