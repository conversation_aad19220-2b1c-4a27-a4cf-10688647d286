import React from 'react'

export const envelope = ({
  height,
  stroke,
  width,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <>
    <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
      <path
        d="M3.5 5.5L9.43079 9.60593C9.77323 9.84301 10.2268 9.84301 10.5692 9.60593L16.5 5.5M4 16H16C17.1046 16 18 15.1046 18 14V6C18 4.89543 17.1046 4 16 4H4C2.89543 4 2 4.89543 2 6V14C2 15.1046 2.89543 16 4 16Z"
        stroke={stroke || light}
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </>
)
