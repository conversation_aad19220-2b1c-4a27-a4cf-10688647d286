import React from 'react'

export const deliveryTruck = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      primary: { dark, darker, main, lightest },
    },
  },
  wrapperColor,
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      d="M20.1462 10.5L19.4615 10.1538L18.9846 8.23846C18.9007 7.90635 18.7083 7.61174 18.438 7.40133C18.1677 7.19093 17.8349 7.07676 17.4923 7.07692H14.8462V5.53846C14.8462 5.13044 14.6841 4.73912 14.3955 4.4506C14.107 4.16209 13.7157 4 13.3077 4H2.53846C2.13044 4 1.73912 4.16209 1.4506 4.4506C1.16209 4.73912 1 5.13044 1 5.53846V14.7692C1 15.1773 1.16209 15.5686 1.4506 15.8571C1.73912 16.1456 2.13044 16.3077 2.53846 16.3077H19.4615C19.8696 16.3077 20.2609 16.1456 20.5494 15.8571C20.8379 15.5686 21 15.1773 21 14.7692V11.8769C20.9998 11.5907 20.9199 11.3102 20.769 11.067C20.6182 10.8237 20.4025 10.6274 20.1462 10.5Z"
      fill={
        (fill === main && main) ||
        (wrapperColor && wrapperColor !== main && `${light}99`) ||
        (fill === light && light) ||
        fill
      }
    />
    <path
      d="M19.4616 10.1539L18.9847 8.23847C18.9007 7.90636 18.7084 7.61175 18.438 7.40135C18.1677 7.19094 17.8349 7.07677 17.4923 7.07693H14.8462V16.3077H19.4616C19.8696 16.3077 20.2609 16.1456 20.5494 15.8571C20.8379 15.5686 21 15.1773 21 14.7692V11.8769C20.9999 11.5907 20.9199 11.3102 20.7691 11.067C20.6182 10.8237 20.4025 10.6274 20.1462 10.5L19.4616 10.1539Z"
      fill={
        (fill === main && dark) ||
        (wrapperColor && wrapperColor !== main && `${light}99`) ||
        (fill === light && lightest) ||
        fill
      }
    />
    <path
      d="M5.61537 17.8461C6.46503 17.8461 7.15383 17.1574 7.15383 16.3077C7.15383 15.458 6.46503 14.7692 5.61537 14.7692C4.7657 14.7692 4.0769 15.458 4.0769 16.3077C4.0769 17.1574 4.7657 17.8461 5.61537 17.8461Z"
      fill="url(#paint0_linear_5346_245699)"
    />
    <path
      d="M17.923 17.8461C18.7727 17.8461 19.4614 17.1574 19.4614 16.3077C19.4614 15.458 18.7727 14.7692 17.923 14.7692C17.0733 14.7692 16.3845 15.458 16.3845 16.3077C16.3845 17.1574 17.0733 17.8461 17.923 17.8461Z"
      fill="url(#paint1_linear_5346_245699)"
    />
    <path
      d="M11.7692 7.65385H7.15383C7.00082 7.65385 6.85408 7.59306 6.74588 7.48487C6.63769 7.37667 6.5769 7.22993 6.5769 7.07692C6.5769 6.92391 6.63769 6.77717 6.74588 6.66898C6.85408 6.56078 7.00082 6.5 7.15383 6.5H11.7692C11.9222 6.5 12.069 6.56078 12.1772 6.66898C12.2854 6.77717 12.3461 6.92391 12.3461 7.07692C12.3461 7.22993 12.2854 7.37667 12.1772 7.48487C12.069 7.59306 11.9222 7.65385 11.7692 7.65385Z"
      fill={
        (fill === main && light) ||
        (wrapperColor && wrapperColor !== main && wrapperColor) ||
        (fill === light && main) ||
        fill
      }
    />
    <path
      d="M11.7692 10.7308H4.07692C3.92391 10.7308 3.77717 10.67 3.66898 10.5618C3.56078 10.4536 3.5 10.3069 3.5 10.1539C3.5 10.0008 3.56078 9.85411 3.66898 9.74591C3.77717 9.63772 3.92391 9.57693 4.07692 9.57693H11.7692C11.9222 9.57693 12.069 9.63772 12.1772 9.74591C12.2854 9.85411 12.3462 10.0008 12.3462 10.1539C12.3462 10.3069 12.2854 10.4536 12.1772 10.5618C12.069 10.67 11.9222 10.7308 11.7692 10.7308Z"
      fill={
        (fill === main && light) ||
        (wrapperColor && wrapperColor !== main && wrapperColor) ||
        (fill === light && main) ||
        fill
      }
    />
    <defs>
      <linearGradient
        id="paint0_linear_5346_245699"
        x1="4.0769"
        y1="16.3026"
        x2="7.15383"
        y2="16.3026"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          stopColor={
            (fill === main && darker) ||
            (wrapperColor && wrapperColor !== main && light) ||
            (fill === light && dark) ||
            fill
          }
        />
        <stop
          offset="1"
          stopColor={
            (fill === main && darker) ||
            (wrapperColor && wrapperColor !== main && light) ||
            (fill === light && darker) ||
            fill
          }
        />
      </linearGradient>
      <linearGradient
        id="paint1_linear_5346_245699"
        x1="16.3845"
        y1="16.3026"
        x2="19.4614"
        y2="16.3026"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          stopColor={
            (fill === main && darker) ||
            (wrapperColor && wrapperColor !== main && light) ||
            (fill === light && dark) ||
            fill
          }
        />
        <stop offset="1" stopColor={(wrapperColor && wrapperColor !== main && light) || darker} />
      </linearGradient>
    </defs>
  </svg>
)
