import React from 'react'

export const statusConfirmed = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      status: { success },
    },
  },
}) => (
  <svg width={width || 18} height={height || 18} viewBox="0 0 18 18" fill={fill || light}>
    <path
      d="M3.25 8H1.75C1.33578 8 1 8.33579 1 8.75001V16.25C1 16.6642 1.33578 17 1.75 17H3.25C3.66422 17 4 16.6642 4 16.25V8.75001C4 8.33579 3.66422 8 3.25 8ZM13 3.54538C13 4.87088 12.1884 5.61438 11.9601 6.5H15.1389C16.1826 6.5 16.9951 7.36707 17 8.31557C17.0026 8.87613 16.7642 9.4796 16.3925 9.85298L16.3891 9.85641C16.6964 10.5857 16.6465 11.6076 16.0982 12.3398C16.3695 13.149 16.096 14.1431 15.5863 14.676C15.7206 15.2259 15.6564 15.6939 15.3941 16.0707C14.7563 16.9871 13.1755 17 11.8387 17L11.7498 17C10.2408 16.9994 9.00588 16.45 8.01357 16.0086C7.51491 15.7867 7.86291 15.5121 7.36822 15.503C7.16385 15.4993 7 15.3325 7 15.1281V8.44776C7 8.34776 7.04007 8.25179 7.11119 8.18147C8.34913 6.95822 7.88144 5.66313 8.8961 4.64675C9.35872 4.18325 9.52698 3.48313 9.68963 2.80606C9.8286 2.22791 10.1193 1 10.75 1C11.5 1 13 1.25 13 3.54538Z"
      stroke={success}
      strokeWidth="2"
    />
  </svg>
)
