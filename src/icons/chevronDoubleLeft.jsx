import React from 'react'

export const chevronDoubleLeft = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 16} height={height || 16} viewBox="0 0 16 16">
    <path
      d="M8.8 4.79995L12 7.99995L8.8 11.2M4 4.79995L7.2 7.99995L4 11.2"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
