import React from 'react'

export const checkboxSquaredUnchecked = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light, gray2 },
    },
  },
}) => (
  <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" fill="none">
    <rect x="0.5" y="0.5" width={width - 1 || '15'} height={height - 1 || '15'} fill={fill || light} />
    <rect x="0.5" y="0.5" width={width - 1 || '15'} height={height - 1 || '15'} stroke={stroke || gray2} />
  </svg>
)
