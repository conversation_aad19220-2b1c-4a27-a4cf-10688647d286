import React from 'react'

export const blueCheckBadge = ({
  fill,
  height,
  width,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <>
    <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
      <path
        d="M17.645 8.99443L16.5569 7.73036C16.3489 7.49034 16.1809 7.04231 16.1809 6.72229V5.36221C16.1809 4.51416 15.4848 3.81811 14.6368 3.81811H13.2767C12.9647 3.81811 12.5087 3.6501 12.2686 3.44209L11.0046 2.35402C10.4525 1.88199 9.54847 1.88199 8.98843 2.35402L7.73236 3.45009C7.49234 3.6501 7.03631 3.81811 6.72429 3.81811H5.34021C4.49215 3.81811 3.79611 4.51416 3.79611 5.36221V6.73029C3.79611 7.04231 3.6281 7.49034 3.42809 7.73036L2.34802 9.00243C1.88399 9.55447 1.88399 10.4505 2.34802 11.0026L3.42809 12.2746C3.6281 12.5147 3.79611 12.9627 3.79611 13.2747V14.6428C3.79611 15.4908 4.49215 16.1869 5.34021 16.1869H6.72429C7.03631 16.1869 7.49234 16.3549 7.73236 16.5629L8.99643 17.651C9.54847 18.123 10.4525 18.123 11.0126 17.651L12.2766 16.5629C12.5167 16.3549 12.9647 16.1869 13.2847 16.1869H14.6448C15.4928 16.1869 16.1889 15.4908 16.1889 14.6428V13.2827C16.1889 12.9707 16.3569 12.5147 16.5649 12.2746L17.653 11.0106C18.117 10.4585 18.117 9.54647 17.645 8.99443Z"
        fill={fill || dark}
        stroke={fill || dark}
        strokeWidth="1.4"
      />
      <path
        d="M12.7928 8.20703L8.99992 11.9999L7.70703 10.707"
        stroke="#ffffff"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </>
)
