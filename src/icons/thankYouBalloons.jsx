import React from 'react'

export const thankYouBalloons = ({ width, height, centerFill, sideFill }) => (
  <svg width={width || 388} height={height || 205} viewBox="0 0 388 205" fill="none">
    <path
      d="M351.115 30.3716L348.746 28.0027C347.184 26.4406 344.652 26.4406 343.09 28.0027L340.721 30.3716C339.159 31.9337 339.159 34.4663 340.721 36.0284L343.09 38.3972C344.652 39.9593 347.184 39.9593 348.746 38.3972L351.115 36.0284C352.677 34.4663 352.677 31.9337 351.115 30.3716Z"
      stroke={sideFill || '#FF6C01'}
      strokeWidth="2"
      strokeMiterlimit="10"
    />
    <path
      d="M150.29 150.3C153.427 150.3 155.97 147.757 155.97 144.62C155.97 141.483 153.427 138.94 150.29 138.94C147.153 138.94 144.61 141.483 144.61 144.62C144.61 147.757 147.153 150.3 150.29 150.3Z"
      stroke="#424242"
      strokeWidth="2"
      strokeMiterlimit="10"
    />
    <path
      d="M6.63996 38.8703C9.77694 38.8703 12.32 36.3272 12.32 33.1903C12.32 30.0533 9.77694 27.5103 6.63996 27.5103C3.50298 27.5103 0.959961 30.0533 0.959961 33.1903C0.959961 36.3272 3.50298 38.8703 6.63996 38.8703Z"
      fill={centerFill || '#5896DF'}
    />
    <path
      d="M381.78 133.15C384.917 133.15 387.46 130.607 387.46 127.47C387.46 124.333 384.917 121.79 381.78 121.79C378.643 121.79 376.1 124.333 376.1 127.47C376.1 130.607 378.643 133.15 381.78 133.15Z"
      fill={sideFill || '#FF6C01'}
    />
    <path
      d="M134.43 6.05012C135.998 6.05012 137.27 4.7786 137.27 3.21011C137.27 1.64162 135.998 0.370117 134.43 0.370117C132.861 0.370117 131.59 1.64162 131.59 3.21011C131.59 4.7786 132.861 6.05012 134.43 6.05012Z"
      fill={centerFill || '#5896DF'}
    />
    <path
      d="M245.26 171.26C246.828 171.26 248.1 169.989 248.1 168.42C248.1 166.852 246.828 165.58 245.26 165.58C243.691 165.58 242.42 166.852 242.42 168.42C242.42 169.989 243.691 171.26 245.26 171.26Z"
      fill={sideFill || '#FF6C01'}
    />
    <path
      d="M82.91 132.37C102.555 132.37 118.48 113.745 118.48 90.7699C118.48 67.7949 102.555 49.1699 82.91 49.1699C63.2652 49.1699 47.34 67.7949 47.34 90.7699C47.34 113.745 63.2652 132.37 82.91 132.37Z"
      fill="white"
    />
    <path
      d="M61.9399 94.5202C61.9399 111.33 70.4699 125.8 82.7399 132.36H82.9099C102.56 132.36 118.48 113.73 118.48 90.7601C118.48 73.9401 109.95 59.4702 97.6899 52.9102H97.5099C77.8699 52.9202 61.9399 71.5402 61.9399 94.5202Z"
      fill={sideFill || '#FF6C01'}
    />
    <path
      d="M82.91 132.37C102.555 132.37 118.48 113.745 118.48 90.7699C118.48 67.7949 102.555 49.1699 82.91 49.1699C63.2652 49.1699 47.34 67.7949 47.34 90.7699C47.34 113.745 63.2652 132.37 82.91 132.37Z"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
    <path
      d="M195.47 29.1799C191.422 25.6127 186.163 23.729 180.771 23.9149C175.379 24.1008 170.262 26.3424 166.47 30.1799C158.26 38.4899 158.63 51.9899 166.88 60.2499L188.19 81.5499C189.144 82.506 190.277 83.2645 191.525 83.782C192.772 84.2996 194.109 84.566 195.46 84.566C196.811 84.566 198.148 84.2996 199.395 83.782C200.643 83.2645 201.776 82.506 202.73 81.5499L224.03 60.2499C232.29 51.9899 232.66 38.4899 224.45 30.1799C220.659 26.3481 215.546 24.1102 210.159 23.9243C204.771 23.7384 199.517 25.6186 195.47 29.1799Z"
      fill="white"
    />
    <path
      d="M218.46 25.9402C220.691 26.9893 222.722 28.4209 224.46 30.1702C232.67 38.4802 232.3 51.9802 224.04 60.2402L204.5 79.7802L184.96 60.2402C176.71 51.9802 176.34 38.4802 184.55 30.1702C186.284 28.4166 188.316 26.9844 190.55 25.9402C192.335 26.7853 193.992 27.8796 195.47 29.1902C198.038 26.9102 201.13 25.2992 204.47 24.5002C209.143 23.1447 214.161 23.6612 218.46 25.9402Z"
      fill={centerFill || '#5896DF'}
    />
    <path
      d="M195.47 29.1799C191.422 25.6127 186.163 23.729 180.771 23.9149C175.379 24.1008 170.262 26.3424 166.47 30.1799C158.26 38.4899 158.63 51.9899 166.88 60.2499L188.19 81.5499C189.144 82.506 190.277 83.2645 191.525 83.782C192.772 84.2996 194.109 84.566 195.46 84.566C196.811 84.566 198.148 84.2996 199.395 83.782C200.643 83.2645 201.776 82.506 202.73 81.5499L224.03 60.2499C232.29 51.9899 232.66 38.4899 224.45 30.1799C220.659 26.3481 215.546 24.1102 210.159 23.9243C204.771 23.7384 199.517 25.6186 195.47 29.1799V29.1799Z"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
    <path
      d="M89.9099 132.37C89.9099 139.58 75.9099 139.58 75.9099 146.79C75.9099 154 89.9099 154 89.9099 161.21C89.9099 168.42 75.9099 168.42 75.9099 175.63C75.9099 182.84 89.9099 182.84 89.9099 190.05C89.9099 197.26 75.9099 197.27 75.9099 204.48"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
    <path
      d="M286.74 123.99C302.138 123.99 314.62 109.39 314.62 91.38C314.62 73.37 302.138 58.77 286.74 58.77C271.342 58.77 258.86 73.37 258.86 91.38C258.86 109.39 271.342 123.99 286.74 123.99Z"
      fill="white"
    />
    <path
      d="M270.3 94.32C270.3 107.5 276.98 118.84 286.6 123.98H286.74C302.13 123.98 314.61 109.38 314.61 91.3699C314.61 78.1999 307.93 66.86 298.31 61.71H298.18C282.78 61.72 270.3 76.32 270.3 94.32Z"
      fill={sideFill || '#FF6C01'}
    />
    <path
      d="M286.74 123.99C302.138 123.99 314.62 109.39 314.62 91.38C314.62 73.37 302.138 58.77 286.74 58.77C271.342 58.77 258.86 73.37 258.86 91.38C258.86 109.39 271.342 123.99 286.74 123.99Z"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
    <path
      d="M293.74 124C293.74 129.65 279.74 129.65 279.74 135.3C279.74 140.95 293.74 140.95 293.74 146.6C293.74 152.25 279.74 152.25 279.74 157.9C279.74 163.55 293.74 163.55 293.74 169.21C293.74 174.87 279.74 174.86 279.74 180.52"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
    <path
      d="M202.47 83.6104C202.47 89.7004 188.47 89.7004 188.47 95.8004C188.47 101.9 202.47 101.91 202.47 108.01C202.47 114.11 188.47 114.11 188.47 120.21C188.47 126.31 202.47 126.31 202.47 132.42C202.47 138.53 188.47 138.52 188.47 144.62"
      stroke="black"
      strokeWidth="4"
      strokeMiterlimit="10"
    />
  </svg>
)
