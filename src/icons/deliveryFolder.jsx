import React from 'react'

export const deliveryFolder = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light, gray5 },
      primary: { main, darker },
    },
  },
}) => (
  <svg width={width || 31} height={height || 26} viewBox="0 0 31 26">
    <path
      opacity="0.75"
      d="M28.0606 0.272462C29.4664 0.272462 30.606 1.4121 30.606 2.81792V23.1816C30.606 24.5874 29.4664 25.727 28.0605 25.727H2.606C1.20019 25.727 0.0605468 24.5874 0.0605469 23.1816L0.0605478 2.81792C0.0605478 1.4121 1.20019 0.272461 2.606 0.272461L28.0606 0.272462Z"
      fill={light}
    />
    <path
      d="M4.75981 21.8052C4.75981 22.0214 4.93514 22.1968 5.15142 22.1968C5.3677 22.1968 5.54303 22.0214 5.54303 21.8052V19.0021L6.81292 20.272L7.36674 19.7182L5.15147 17.5029L2.9362 19.7182L3.49001 20.272L4.75981 19.0022V21.8052Z"
      fill={fill === main ? darker : gray5}
    />
    <path
      d="M2.01855 21.419H2.80177V22.9854H7.50107V21.419H8.28429V22.9854C8.28429 23.418 7.93363 23.7687 7.50107 23.7687H2.80177C2.36921 23.7687 2.01855 23.418 2.01855 22.9854V21.419Z"
      fill={fill === main ? darker : gray5}
    />
    <path
      d="M10.2422 0.272461H20.424V6.90883C20.424 7.46111 19.9763 7.90883 19.424 7.90883H11.2422C10.6899 7.90883 10.2422 7.46111 10.2422 6.90883V0.272461Z"
      fill={fill === main ? darker : gray5}
    />
  </svg>
)
