import React from 'react'

export const upload = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M3.7962 13.7963C2.17316 13.7963 0.857422 12.6894 0.857422 11.0663C0.857422 9.44329 2.17316 8.12755 3.7962 8.12755C3.90657 8.12755 4.01552 8.13364 4.12273 8.14549V8.12755H4.16315C4.13647 7.91361 4.12273 7.69566 4.12273 7.47449C4.12273 4.58908 6.46181 2.25 9.34722 2.25C11.3011 2.25 13.0045 3.32261 13.9007 4.91114C14.1197 4.87893 14.3438 4.86224 14.5717 4.86224C17.0964 4.86224 19.1431 6.90894 19.1431 9.43367C19.1431 11.512 17.7562 13.0963 15.8574 13.6186M9.85599 17.75V10.25M9.85599 10.25L6.85742 
      13.3431M9.85599 10.25L12.8574 13.3431"
      strokeWidth={strokeWidth || 1}
      stroke={fill || stroke || light}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
