import React from 'react'

export const sizeONE_SIZE = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21ZM11 22C17.0751 22 22 17.0751 22 11C22 4.92487 17.0751 0 11 0C4.92487 0 0 4.92487 0 11C0 17.0751 4.92487 22 11 22Z"
      fill={fill || dark}
      fillOpacity="0.2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.74919 13.8194C4.20626 14.0887 4.71788 14.2234 5.28403 14.2234C5.85018 14.2234 6.35919 14.0887 6.81108 13.8194C7.26296 13.55 7.61875 13.1804 7.87845 12.7105C8.13815 12.2349 8.26801 11.7019 8.26801 11.1117C8.26801 10.5214 8.13815 9.99137 7.87845 9.52146C7.61875 9.04583 7.26296 8.67334 6.81108 8.404C6.35919 8.13467 5.85018 8 5.28403 8C4.71788 8 4.20626 8.13467 3.74919 8.404C3.29731 8.67334 2.94151 9.04583 2.68181 9.52146C2.4273 9.99137 2.30005 10.5214 2.30005 11.1117C2.30005 11.7019 2.4273 12.2349 2.68181 12.7105C2.94151 13.1804 3.29731 13.55 3.74919 13.8194ZM6.15663 12.7965C5.89692 12.9569 5.60606 13.0372 5.28403 13.0372C4.962 13.0372 4.67113 12.9569 4.41143 12.7965C4.15173 12.6303 3.94656 12.4011 3.79593 12.1088C3.6505 11.8165 3.57778 11.4842 3.57778 11.1117C3.57778 10.7392 3.6505 10.4068 3.79593 10.1146C3.94656 9.82231 4.15173 9.59596 4.41143 9.4355C4.67113 9.26932 4.962 9.18622 5.28403 9.18622C5.60606 9.18622 5.89692 9.26932 6.15663 9.4355C6.41633 9.59596 6.6189 9.82231 6.76433 10.1146C6.91496 10.4068 6.99027 10.7392 6.99027 11.1117C6.99027 11.4842 6.91496 11.8165 6.76433 12.1088C6.6189 12.4011 6.41633 12.6303 6.15663 12.7965ZM14.1885 8V14.2258H13.0452L10.0453 10.4459V14.2258H8.66998V8H9.82182L12.8132 11.78V8H14.1885ZM19.2999 13.0696V14.2258H14.641V8H19.1882V9.15623H16.0249V10.5081H18.8186V11.6288H16.0249V13.0696H19.2999Z"
      fill={fill || dark}
    />
  </svg>
)
