import React from 'react'

export const careTumbleDryLowTemp = ({
  width,
  height,
  fill,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 28} height={height || 28} viewBox="0 0 28 28" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.9055 26.2117L25.9533 26.154H25.9557C25.9603 26.1234 25.9826 26.0927 26.0012 26.067C26.0118 26.0525 26.0212 26.0395 26.0256 26.0291C26.031 26.0159 26.042 25.9953 26.0537 25.9734C26.0677 25.9471 26.0828 25.9189 26.0906 25.8993C26.4473 24.909 26.2015 18.3544 26.0617 17.78C25.7866 18.1598 25.5688 18.6601 25.3457 19.1724C25.1368 19.6521 24.9234 20.1422 24.6541 20.5537C22.5463 23.7729 20.5529 24.6939 19.2591 25.2916C18.6016 25.5954 18.1248 25.8157 17.9055 26.2117ZM2.21139 18.2131C2.16766 18.1741 2.08863 18.1749 1.80767 18.0972V22.3876C1.80767 22.5717 1.78998 22.8322 1.76965 23.1317C1.69627 24.2128 1.5885 25.8004 2.15474 26.1204C2.52832 26.3343 9.19258 26.231 10.1398 26.1949C9.87557 25.8542 9.40223 25.622 8.78206 25.3178C7.85788 24.8644 6.60763 24.2511 5.23741 22.8804C3.54809 21.1904 3.13793 20.2148 2.79312 19.3946C2.67058 19.1031 2.55629 18.8312 2.39576 18.5539C2.26324 18.3277 2.25432 18.2513 2.21139 18.2131ZM6.44493 6.45909C3.94553 8.92757 3.28513 11.2687 3.28513 14.8356H3.29718C3.29718 17.4939 5.18679 20.3782 6.67149 21.8204C8.61895 23.724 11.4413 24.6542 14.2661 24.635C19.8144 24.5917 24.7289 19.8807 24.7289 14.2563C24.7289 11.0139 23.2442 7.89883 21.5691 6.39179C17.127 2.39703 10.7448 2.20715 6.44493 6.45909ZM26.0979 1.87546C24.9675 1.6351 19.007 1.69519 17.7055 1.87546C18.1366 2.224 18.5901 2.4204 19.0854 2.63496C19.526 2.82581 19.9998 3.03103 20.5206 3.37048C23.5588 5.3342 24.1149 6.46233 25.3272 8.92113C25.5334 9.33948 25.7586 9.79635 26.0183 10.3024C26.404 9.16312 26.3027 3.33924 26.0979 1.87546ZM1.78597 2.82006L1.79802 9.87939V9.88419C2.3691 9.75072 2.38937 9.68246 2.50528 9.29219C2.60854 8.94454 2.78768 8.34138 3.49964 7.20901C5.16289 4.57589 6.95406 3.66869 8.85589 2.70544C9.33244 2.46408 9.81593 2.21919 10.3061 1.94276C9.37335 1.74807 2.64642 1.53895 2.16438 1.94276C1.97201 2.09494 1.89608 2.09134 1.86203 2.12735C1.81971 2.17209 1.84206 2.278 1.78597 2.82006ZM0.0024089 26.8343V1.09669H0C0 -0.097779 0.664524 -0.0443975 1.67542 0.0368082C1.9603 0.0596924 2.27268 0.0847862 2.60545 0.0847862L27.2765 0.0679612C27.626 0.114589 27.8069 0.123271 27.9004 0.218118C28.0093 0.328615 27.9995 0.556059 27.9995 1.09669V26.8343C27.9995 26.8878 27.9997 26.9389 27.9998 26.9877C28.0023 27.8811 28.0026 28 27.0885 28H1.05809C0.00197908 28 0.00198081 27.9954 0.00239217 26.9016C0.00240045 26.8796 0.0024089 26.8572 0.0024089 26.8343ZM13.4973 11.8816C10.4917 12.6291 11.6607 16.833 14.618 16.1672C17.0982 15.6072 16.6209 11.1197 13.4973 11.8816Z"
      fill={fill || main}
    />
  </svg>
)
