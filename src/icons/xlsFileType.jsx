import React from 'react'

export const xlsFileType = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
}) => (
  <svg width={width || 16} height={height || 20} viewBox="0 0 16 20" fill="none">
    <g>
      <path
        d="M3.12051 0H9.81393L15.4782 5.91241V17.3942C15.4782 18.8321 14.3176 20 12.8796 20H3.12051C1.68985 20 0.521973 18.8321 0.521973 17.3942V2.59853C0.521947 1.16788 1.68985 0 3.12051 0Z"
        fill={fill || '#00875A'}
      />
      <path
        d="M0.845428 2.59853V2.59853C0.845406 1.34652 1.86848 0.323455 3.12051 0.323455H9.67587L15.1547 6.04235V17.3942C15.1547 18.6547 14.1378 19.6765 12.8796 19.6765H3.12051C1.86908 19.6765 0.845428 18.6541 0.845428 17.3942V2.59853Z"
        stroke={stroke || '#172B4D'}
        strokeOpacity={strokeOpacity || '0.1'}
        strokeWidth={strokeWidth || '0.647'}
      />
      <g opacity="0.3">
        <path d="M9.80664 0V5.86131H15.4782L9.80664 0Z" fill={fillCorner || 'white'} />
      </g>
      <g>
        <path
          d="M6.74074 14.5621H5.76994L5.10572 13.4453L4.44148 14.5621H3.46338L4.61667 12.6205L3.60209 10.9343H4.58016L5.1057 11.8102L5.62393 10.9343H6.60203L5.59472 12.6278L6.74074 14.5621ZM7.0838 14.5621V10.9343H8.01081V13.7737H9.58745V14.5621H7.0838V14.5621ZM11.1203 14.6059C10.7553 14.6059 10.4342 14.4891 10.1568 14.2628C9.88672 14.0438 9.73346 13.7665 9.71155 13.438L10.5072 13.2044C10.5291 13.3796 10.6021 13.5329 10.7262 13.6497C10.8575 13.7665 11.0035 13.8249 11.1641 13.8249C11.2955 13.8249 11.405 13.7957 11.4926 13.7373C11.5729 13.6789 11.6167 13.5986 11.6167 13.5037C11.6167 13.4234 11.5802 13.3577 11.5145 13.2993C11.4488 13.2482 11.3612 13.2044 11.259 13.1679C11.1568 13.1315 11.0327 13.0949 10.9086 13.0658C10.7772 13.0293 10.6458 12.9855 10.5218 12.9271C10.3904 12.876 10.2736 12.8103 10.1714 12.73C10.0619 12.657 9.98161 12.5475 9.91591 12.4088C9.85021 12.2774 9.81372 12.1168 9.81372 11.9344C9.81372 11.6351 9.93779 11.3869 10.1933 11.1898C10.4487 10.9855 10.7553 10.8906 11.113 10.8906C11.4706 10.8906 11.7845 10.9781 12.0473 11.146C12.3101 11.3212 12.4779 11.5475 12.5436 11.8249L11.7115 12.1752C11.675 12.022 11.602 11.8979 11.4998 11.803C11.3976 11.7154 11.2663 11.6643 11.113 11.6643C10.9962 11.6643 10.9086 11.6935 10.8356 11.7373C10.7699 11.7811 10.7407 11.8468 10.7407 11.9271C10.7407 12.0001 10.7845 12.0658 10.8721 12.1169C10.9597 12.1607 11.0765 12.1971 11.2079 12.2191C11.3392 12.241 11.4852 12.2775 11.6385 12.3286C11.7991 12.3869 11.9378 12.4453 12.0765 12.5256C12.2078 12.5986 12.3173 12.7227 12.4049 12.8833C12.4998 13.0512 12.5436 13.2483 12.5436 13.4745C12.5436 13.8103 12.4122 14.0877 12.1494 14.2921C11.8867 14.4964 11.5437 14.6059 11.1203 14.6059Z"
          fill={colorText || 'white'}
        />
      </g>
    </g>
  </svg>
)
