import React from 'react'

export const bill = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light, gray5 },
      primary: { main, darker },
    },
  },
}) => (
  <svg width={width || 27} height={height || 32} viewBox="0 0 27 32">
    <path
      opacity="0.75"
      d="M4.62804 3.45609C4.94397 3.70816 5.41867 3.70681 5.73295 3.45309L8.87074 0.918898C9.18585 0.663753 9.66303 0.663753 9.97814 0.918898L13.1126 3.45084C13.4284 3.70523 13.9052 3.70523 14.2208 3.45084L17.3553 0.918898C17.6704 0.663753 18.1476 0.663753 18.4627 0.918898L21.6005 3.45309C21.9148 3.70681 22.3895 3.70816 22.7054 3.45609L25.0133 1.61162C25.3571 1.33726 25.8822 1.36668 26.1859 1.67728C26.3199 1.81431 26.3939 1.99081 26.394 2.17369V29.8269C26.3937 30.2414 26.0217 30.5772 25.5628 30.577C25.3604 30.5769 25.165 30.51 25.0133 30.389L22.7054 28.5444C22.3895 28.2924 21.9148 28.2937 21.6005 28.5474L18.4627 31.0816C18.1476 31.3368 17.6704 31.3368 17.3553 31.0816L14.2208 28.5497C13.9051 28.2953 13.4284 28.2953 13.1126 28.5497L9.97814 31.0816C9.66303 31.3368 9.18585 31.3368 8.87074 31.0816L5.73295 28.5474C5.41867 28.2937 4.94397 28.2924 4.62804 28.5444L2.32018 30.389C1.97633 30.6633 1.45129 30.6339 1.14756 30.3233C1.01356 30.1863 0.939536 30.0098 0.939453 29.8269V2.17361C0.939702 1.75915 1.3118 1.42333 1.77063 1.42356C1.97309 1.42364 2.16848 1.4905 2.32018 1.61154L4.62804 3.45609Z"
      fill={light}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.03027 10.3818C6.03027 9.82955 6.47799 9.38184 7.03027 9.38184H15.2121C15.7644 9.38184 16.2121 9.82955 16.2121 10.3818V10.9273C16.2121 11.4796 15.7644 11.9273 15.2121 11.9273H7.03027C6.47799 11.9273 6.03027 11.4796 6.03027 10.9273V10.3818ZM6.03027 15.4727C6.03027 14.9205 6.47799 14.4727 7.03027 14.4727H20.303C20.8553 14.4727 21.303 14.9205 21.303 15.4727V16.0182C21.303 16.5705 20.8553 17.0182 20.303 17.0182H7.03027C6.47799 17.0182 6.03027 16.5705 6.03027 16.0182V15.4727ZM7.03027 19.5637C6.47799 19.5637 6.03027 20.0114 6.03027 20.5637V21.1091C6.03027 21.6614 6.47799 22.1091 7.03027 22.1091H20.303C20.8553 22.1091 21.303 21.6614 21.303 21.1091V20.5637C21.303 20.0114 20.8553 19.5637 20.303 19.5637H7.03027Z"
      fill={fill === main ? darker : gray5}
    />
  </svg>
)
