import React from 'react'

export const fileFalse = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M8.99976 18H5.49975C4.39518 17.9999 3.49975 17.1045 3.49976 15.9999L3.49983 3.99999C3.49984 2.89542 4.39527 2 5.49983 2H14.5001C15.6046 2 16.5001 2.89543 16.5001 4V10.5M16.5001 18L14.5001 16M14.5001 16L12.5001 14M14.5001 16L12.5001 18M14.5001 16L16.5001 14M7.00007 6H13.0001M7.00007 9H13.0001M7.00007 12H10.0001"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
