import React from 'react'

export const fileCheck = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M8.58318 18H4.58317C3.4786 17.9999 2.58317 17.1045 2.58318 15.9999L2.58325 3.99999C2.58326 2.89542 3.47869 2 4.58325 2H13.5835C14.688 2 15.5835 2.89543 15.5835 4V9.5M11.5835 15.1667L13.4168 17L17.4168 12.9998M6.08349 6H12.0835M6.08349 9H12.0835M6.08349 12H9.08349"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
