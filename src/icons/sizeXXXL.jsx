import React from 'react'

export const sizeXXXL = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 18} height={height || 6} viewBox="0 0 18 6">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.96751 5.65824L2.68054 3.81266L1.41642 5.65824H0L1.97233 2.95443L0.0989973 0.341766H1.50019L2.72623 2.06582L3.92943 0.341766H5.82932L7.05318 2.06276L8.25424 0.341766H10.1541L11.3802 2.06582L12.5834 0.341766H13.916L12.0579 2.90886L14.0455 5.65824H12.6214L11.3345 3.81266L10.0704 5.65824H8.29232L7.00753 3.81579L5.74555 5.65824H3.96751ZM3.40398 2.90886L4.84097 0.923567L6.29714 2.95443L4.86058 4.92376L3.40398 2.90886ZM7.73311 2.90886L9.1701 0.923567L10.6263 2.95443L9.18971 4.92376L7.73311 2.90886Z"
      fill={fill || dark}
    />
    <path d="M15.3272 0.341766H14.0935V5.65824H18.0001V4.6557H15.3272V0.341766Z" fill={fill || dark} />
  </svg>
)
