import React from 'react'

export const calendarEdit = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M4.35413 7.05762H17.4791M6.05055 1.66699V3.07341M15.6041 1.66699V3.07324M15.6041 3.07324H6.22913C4.67583 3.07324 3.41663 4.33244 3.41663 5.88574V15.2608C3.41663 16.8141 4.67583 18.0733 6.22913 18.0733H15.6041C17.1574 18.0733 18.4166 16.8141 18.4166 15.2608L18.4166 5.88574C18.4166 4.33244 17.1574 3.07324 15.6041 3.07324ZM8.57288 12.6827L9.97913 14.0889L13.2604 10.8077"
      stroke={stroke || fill || dark}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={strokeWidth || 2}
    />
  </svg>
)
