import React from 'react'

export const contracts = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M16.6348 9.72482L16.1396 9.23006C15.9743 9.3955 15.9026 9.63256 15.9484 9.8619L16.6348 9.72482ZM16.1195 11.4294L16.6149 11.924L16.6149 11.9239L16.1195 11.4294ZM7.26691 4.91833L6.77175 5.41312C6.99759 5.63913 7.34781 5.68354 7.6229 5.52105L7.26691 4.91833ZM3.41427 9.7749L4.10071 9.91198C4.14651 9.68265 4.07475 9.44559 3.90946 9.28015L3.41427 9.7749ZM3.92958 11.4794L3.43421 11.974L3.43423 11.974L3.92958 11.4794ZM5.52887 13.0812L5.03352 13.5758C5.25684 13.7994 5.60234 13.8457 5.87664 13.6887L5.52887 13.0812ZM5.53102 13.08L6.02804 12.587C5.80486 12.362 5.45832 12.315 5.18326 12.4725L5.53102 13.08ZM9.11403 16.6718L8.61868 17.1664L9.11403 16.6718ZM10.6962 15.0872L11.1896 14.5908C10.9158 14.3186 10.4733 14.3195 10.2006 14.5928C9.92795 14.8661 9.92802 15.3086 10.2008 15.5818L10.6962 15.0872ZM11.0679 15.4573L11.5632 14.9627L11.0679 15.4573ZM12.65 15.4573L13.1454 15.9519L12.65 15.4573ZM12.6731 13.8971L13.1697 13.4037C12.8998 13.1321 12.4617 13.1277 12.1865 13.3939C11.9112 13.6601 11.9009 14.098 12.1634 14.3769L12.6731 13.8971ZM12.9879 14.1816L12.4925 14.6762L12.9879 14.1816ZM14.5544 14.1969L15.0402 14.7009L15.0402 14.7009L14.5544 14.1969ZM14.5544 12.5814L15.0498 12.0868L15.048 12.0851L14.5544 12.5814ZM10.6887 10.9188C10.4156 10.6452 9.97234 10.6449 9.69876 10.9181C9.42519 11.1912 9.42485 11.6344 9.69801 11.908L10.6887 10.9188ZM8.93285 12.3304C8.65969 12.0568 8.21648 12.0565 7.9429 12.3296C7.66933 12.6028 7.66899 13.046 7.94215 13.3196L8.93285 12.3304ZM12.8296 6.74924C13.1093 7.01614 13.5524 7.00577 13.8193 6.72608C14.0862 6.44639 14.0758 6.0033 13.7961 5.7364L12.8296 6.74924ZM12.5351 5.50062L12.0518 6.00702L12.0518 6.00704L12.5351 5.50062ZM9.45037 5.53792L8.95502 5.04333L9.45037 5.53792ZM7.38093 7.61053L7.87628 8.10513L7.38093 7.61053ZM8.86659 9.09847L8.37124 8.60387L8.86659 9.09847ZM11.254 7.69812C11.5272 7.42454 11.5268 6.98133 11.2532 6.70817C10.9797 6.43501 10.5365 6.43535 10.2633 6.70892L11.254 7.69812ZM2.41144 5.9449L1.91609 5.4503L2.41144 5.9449ZM6.76622 4.41727L6.27106 4.91207L6.76622 4.41727ZM3.9362 4.41783L3.44086 3.92323L3.9362 4.41783ZM17.5886 5.94396L17.0932 6.43856L17.5886 5.94396ZM16.0639 4.41701L15.5686 4.9116L16.0639 4.41701ZM12.7424 5.89875L13.729 4.91131L12.7386 3.9218L11.7521 4.90924L12.7424 5.89875ZM15.5686 4.9116L17.0932 6.43856L18.0839 5.44937L16.5593 3.92241L15.5686 4.9116ZM17.0929 8.27593L16.1396 9.23006L17.13 10.2196L18.0833 9.26544L17.0929 8.27593ZM15.9484 9.8619C16.0262 10.2515 15.9043 10.6541 15.6241 10.9348L16.6149 11.9239C17.2257 11.3122 17.4906 10.4356 17.3213 9.58774L15.9484 9.8619ZM15.6242 10.9348L14.2306 12.3305L15.2213 13.3196L16.6149 11.924L15.6242 10.9348ZM10.1813 4.83857C9.43584 4.09197 8.61689 3.93651 7.97967 3.99366C7.67199 4.02125 7.41491 4.09686 7.23377 4.16557C7.14249 4.2002 7.06828 4.23392 7.014 4.26066C6.98679 4.27406 6.96439 4.28581 6.94714 4.29518C6.93851 4.29987 6.93115 4.30398 6.9251 4.30741C6.92208 4.30913 6.91938 4.31068 6.91701 4.31205C6.91583 4.31273 6.91473 4.31337 6.91371 4.31397C6.9132 4.31427 6.91272 4.31455 6.91225 4.31483C6.91202 4.31496 6.91168 4.31516 6.91156 4.31523C6.91123 4.31542 6.91091 4.31562 7.26691 4.91833C7.6229 5.52105 7.62259 5.52123 7.62228 5.52142C7.62218 5.52147 7.62188 5.52165 7.62168 5.52177C7.62129 5.522 7.62092 5.52222 7.62056 5.52242C7.61985 5.52284 7.61921 5.52321 7.61864 5.52354C7.61749 5.52421 7.61662 5.52471 7.61602 5.52505C7.61481 5.52573 7.61467 5.5258 7.61556 5.52531C7.61735 5.52434 7.62317 5.52122 7.63271 5.51652C7.65192 5.50706 7.68528 5.49164 7.73032 5.47456C7.82183 5.43984 7.95239 5.40172 8.10472 5.38806C8.38754 5.3627 8.77811 5.41465 9.19059 5.82776L10.1813 4.83857ZM7.76206 4.42354L7.26137 3.92248L6.27106 4.91207L6.77175 5.41312L7.76206 4.42354ZM3.44086 3.92323L1.91609 5.4503L2.90679 6.4395L4.43155 4.91243L3.44086 3.92323ZM1.91668 9.26637L2.91907 10.2697L3.90946 9.28015L2.90708 8.27687L1.91668 9.26637ZM2.72782 9.63782C2.55851 10.4856 2.82343 11.3623 3.43421 11.974L4.42495 10.9849C4.14476 10.7042 4.0229 10.3016 4.10071 9.91198L2.72782 9.63782ZM3.43423 11.974L5.03352 13.5758L6.02423 12.5866L4.42493 10.9849L3.43423 11.974ZM5.87664 13.6887L5.87878 13.6875L5.18326 12.4725L5.18111 12.4737L5.87664 13.6887ZM5.034 13.5729C5.31727 13.8585 8.5298 17.0774 8.61868 17.1664L9.60939 16.1772C9.51917 16.0869 6.30914 12.8705 6.02804 12.587L5.034 13.5729ZM8.61868 17.1664C9.32902 17.8779 10.4812 17.8779 11.1915 17.1664L10.2008 16.1772C10.0374 16.3409 9.77282 16.3409 9.60939 16.1772L8.61868 17.1664ZM11.1915 17.1664C11.9013 16.4556 11.9013 15.3035 11.1915 14.5927L10.2008 15.5818C10.3649 15.7461 10.3648 16.013 10.2008 16.1772L11.1915 17.1664ZM10.6962 15.0872C10.2027 15.5837 10.2027 15.5837 10.2027 15.5837C10.2027 15.5837 10.2027 15.5837 10.2027 15.5837C10.2027 15.5837 10.2027 15.5837 10.2027 15.5838C10.2028 15.5838 10.2028 15.5838 10.2029 15.5839C10.203 15.5841 10.2032 15.5843 10.2035 15.5845C10.2041 15.5851 10.2049 15.5859 10.2059 15.5869C10.2081 15.5891 10.2112 15.5922 10.2153 15.5962C10.2233 15.6042 10.2349 15.6158 10.2491 15.6299C10.2775 15.6581 10.3163 15.6966 10.3575 15.7377C10.4405 15.8202 10.5325 15.9118 10.5725 15.9519L11.5632 14.9627C11.5212 14.9206 11.4273 14.8272 11.345 14.7452C11.3036 14.704 11.2647 14.6654 11.2363 14.6371C11.222 14.623 11.2104 14.6114 11.2023 14.6033C11.1982 14.5993 11.1951 14.5962 11.1929 14.594C11.1919 14.593 11.191 14.5922 11.1905 14.5916C11.1902 14.5913 11.19 14.5911 11.1899 14.591C11.1898 14.5909 11.1897 14.5909 11.1897 14.5908C11.1897 14.5908 11.1897 14.5908 11.1897 14.5908C11.1897 14.5908 11.1897 14.5908 11.1897 14.5908C11.1896 14.5908 11.1896 14.5908 10.6962 15.0872ZM10.5725 15.9519C11.2829 16.6633 12.435 16.6633 13.1454 15.9519L12.1547 14.9627C11.9912 15.1264 11.7267 15.1264 11.5632 14.9627L10.5725 15.9519ZM13.1454 15.9519C13.8426 15.2536 13.8543 14.1306 13.1828 13.4172L12.1634 14.3769C12.3186 14.5417 12.3154 14.8018 12.1547 14.9627L13.1454 15.9519ZM12.1765 14.3904C12.2508 14.4653 12.3224 14.5252 12.3694 14.5651C12.419 14.6074 12.4539 14.6376 12.4925 14.6762L13.4832 13.687C13.4012 13.6049 13.3287 13.5432 13.2761 13.4985C13.2208 13.4515 13.1953 13.4295 13.1697 13.4037L12.1765 14.3904ZM12.4925 14.6762C13.1945 15.3793 14.3279 15.3875 15.0402 14.7009L14.0687 13.6929C13.905 13.8507 13.6447 13.8487 13.4832 13.687L12.4925 14.6762ZM15.0402 14.7009C15.7866 13.9815 15.7574 12.7956 15.0498 12.0868L14.059 13.076C14.2385 13.2558 14.2262 13.541 14.0686 13.693L15.0402 14.7009ZM15.048 12.0851L10.4496 7.51129L9.46227 8.5039L14.0608 13.0777L15.048 12.0851ZM13.2568 13.4908L10.6887 10.9188L9.69801 11.908L12.266 14.48L13.2568 13.4908ZM11.3198 14.721L8.93285 12.3304L7.94215 13.3196L10.329 15.7101L11.3198 14.721ZM13.7961 5.7364L13.0183 4.9942L12.0518 6.00704L12.8296 6.74924L13.7961 5.7364ZM13.0184 4.99421C11.8752 3.9033 10.0715 3.92517 8.95502 5.04333L9.94572 6.03252C10.5246 5.45273 11.4592 5.44146 12.0518 6.00702L13.0184 4.99421ZM8.95502 5.04333L6.88557 7.11594L7.87628 8.10513L9.94572 6.03252L8.95502 5.04333ZM6.88557 7.11594C6.20247 7.80009 6.20247 8.90891 6.88557 9.59306L7.87628 8.60387C7.7389 8.46628 7.7389 8.24272 7.87628 8.10513L6.88557 7.11594ZM6.88557 9.59306C7.56927 10.2778 8.67824 10.2778 9.36194 9.59306L8.37124 8.60387C8.23445 8.74087 8.01307 8.74087 7.87628 8.60387L6.88557 9.59306ZM9.36194 9.59306L11.254 7.69812L10.2633 6.70892L8.37124 8.60387L9.36194 9.59306ZM1.91609 5.4503C0.863374 6.50461 0.863636 8.21238 1.91668 9.26637L2.90708 8.27687C2.40005 7.76939 2.39993 6.94713 2.90679 6.4395L1.91609 5.4503ZM7.26137 3.92248C6.20651 2.86684 4.49531 2.86718 3.44086 3.92323L4.43155 4.91243C4.93925 4.40396 5.76316 4.4038 6.27106 4.91207L7.26137 3.92248ZM17.0932 6.43856C17.6001 6.9462 17.5999 7.76846 17.0929 8.27593L18.0833 9.26544C19.1364 8.21146 19.1366 6.50369 18.0839 5.44937L17.0932 6.43856ZM13.729 4.91131C14.2369 4.40297 15.0609 4.4031 15.5686 4.9116L16.5593 3.92241C15.5048 2.86629 13.7935 2.86602 12.7386 3.9218L13.729 4.91131Z"
      fill={fill}

    />
  </svg>
)
