import React from 'react'

export const briefcase = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray6 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M6.5 16V7M13 16V7M7 6.5V4.5C7 3.94772 7.44771 3.5 8 3.5H11.5C12.0523 3.5 12.5 3.94772 12.5 4.5V6.5M4 16.5H16C17.1046 16.5 18 15.6046 18 14.5V8.5C18 7.39543 17.1046 6.5 16 6.5H4C2.89543 6.5 2 7.39543 2 8.5V14.5C2 15.6046 2.89543 16.5 4 16.5Z"
      stroke={fill || stroke || gray6}
      strokeLinecap="round"
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
