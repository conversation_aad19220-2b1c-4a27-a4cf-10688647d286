import React from 'react'

export const linkSign = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray5 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M6.14876 8.49164L4.28754 10.3528C3.59243 11.048 3.1927 11.9938 3.20001 12.9878C3.20731 13.9818 3.5983 14.9334 4.32639 15.639C5.03196 16.3671 5.98374 16.7581 6.97759 16.7654C7.99411 16.7728 8.91753 16.3956 9.61268 15.7005L11.4739 13.8393M13.8512 11.5074L15.7125 9.64619C16.4076 8.95108 16.8073 8.00528 16.8 7.01127C16.7927 6.01725 16.4017 5.06568 15.6736 4.36007C14.9682 3.65467 14.0166 3.26366 13.0226 3.25635C12.0286 3.24905 11.0826 3.62609 10.3875 4.32122L8.52627 6.18244M7.17759 12.7722L12.7612 7.18852"
      stroke={stroke || fill || gray5}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={strokeWidth || 1.14}
    />
  </svg>
)
