import React from 'react'

export const checkboxSquaredChecked = ({
  checkboxColor,
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light, gray5 },
    },
  },
}) => (
  <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" fill="none">
    <rect
      x="0.5"
      y="0.5"
      width={width - 1 || '15'}
      height={height - 1 || '15'}
      fill={checkboxColor || light}
    />
    <path
      d="M12 5L6.03374 11L4 8.95476"
      stroke={stroke || gray5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="0.5" y="0.5" width={width - 1 || '15'} height={height - 1 || '15'} stroke={stroke || gray5} />
  </svg>
)
