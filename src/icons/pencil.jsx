import React from 'react'

export const pencil = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 14} height={height || 15} viewBox="0 0 14 15">
    <g clipPath="url(#clip0_5845_255345)">
      <path
        opacity="0.5"
        d="M12.0903 5.29041L9.20611 2.40625L0.906851 10.706C0.869518 10.7433 0.842684 10.7906 0.829851 10.8413L0.00851784 14.1377C-0.0159822 14.2369 0.0131845 14.3425 0.0855178 14.4148C0.140351 14.4697 0.215018 14.5 0.291434 14.5C0.314768 14.5 0.338684 14.4971 0.362018 14.4912L3.65843 13.6699C3.70977 13.6571 3.75643 13.6302 3.79377 13.5929C6.55897 10.8251 9.3249 8.05805 12.0903 5.29041Z"
        fill={dark}
      />
      <path
        d="M13.578 1.73805L12.7532 0.913219C12.2019 0.361969 11.2412 0.362552 10.6905 0.913219L9.68018 1.92355L12.5677 4.81106L13.578 3.80072C13.8533 3.52539 14.005 3.15905 14.005 2.76939C14.005 2.37972 13.8533 2.01339 13.578 1.73805Z"
        fill={dark}
      />
    </g>
    <defs>
      <clipPath id="clip0_5845_255345">
        <rect width="14" height="14" fill="white" transform="translate(0 0.5)" />
      </clipPath>
    </defs>
  </svg>
)
