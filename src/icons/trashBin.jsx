import React from 'react'

export const trashBin = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M2 4.5H18M7 1.5H13M13.5 18.5H6.5C5.39543 18.5 4.5 17.6046 4.5 16.5L4.0434 5.54163C4.01973 4.97351 4.47392 4.5 5.04253 4.5H14.9575C15.5261 4.5 15.9803 4.97351 15.9566 5.54163L15.5 16.5C15.5 17.6046 14.6046 18.5 13.5 18.5Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
    />
  </svg>
)
