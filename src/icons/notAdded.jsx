import React from 'react'

export const notAdded = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      primary: { lightest, main: primaryMain },
      general: { light, gray2 },
      secondary: { main },
    },
  },
  strokeOpacity,
  fillCorner,
  colorText,
}) => (
  <svg width={width || 56} height={height || 56} viewBox="0 0 56 56" fill="none">
    <path
      d="M56 28C56 43.464 43.464 56 28 56C20.492 56 13.6742 53.0449 8.6462 48.2344C8.43609 48.0333 9.24941 47.6087 9.04564 47.4013C7.57203 45.9013 6.26609 44.2361 5.15812 42.436C5.09064 42.3264 4.00356 42.4366 3.93761 42.326C1.43675 38.1348 0 33.2352 0 28C0 12.536 12.536 0 28 0C43.464 0 56 12.536 56 28Z"
      fill={lightest}
    />
    <path
      d="M3.84961 46.5938C3.84961 47.5602 4.63311 48.3438 5.59961 48.3438H50.2246C51.1911 48.3438 51.9746 47.5602 51.9746 46.5938V13.3438C51.6642 12.8235 50.9194 12.25 50.8809 12.25H4.94336C4.90486 12.25 4.16002 12.8235 3.84961 13.3438V46.5938Z"
      fill={light}
    />
    <path
      d="M5.59961 47.8438C4.90925 47.8438 4.34961 47.2841 4.34961 46.5938V13.4935C4.47273 13.3235 4.65335 13.1363 4.83432 12.9722C4.93554 12.8804 5.02837 12.8035 5.09705 12.75H50.7272C50.7958 12.8035 50.8887 12.8804 50.9899 12.9722C51.1709 13.1363 51.3515 13.3235 51.4746 13.4935V46.5938C51.4746 47.2841 50.915 47.8438 50.2246 47.8438H5.59961Z"
      stroke="url(#paint0_linear_10146_6013)"
      strokeOpacity="0.1"
    />
    <path
      d="M3.9375 13.2344H8.75L9.85075 12.4292H19.6805L20.7812 13.2344H52.0625V8.85938C52.0625 8.2553 51.5728 7.76562 50.9688 7.76562H5.03125C4.42717 7.76562 3.9375 8.2553 3.9375 8.85938V13.2344Z"
      fill={primaryMain}
    />
    <path
      d="M20.7812 11.0469V13.2344H8.75V11.0469C8.75 10.4428 9.23967 9.95312 9.84375 9.95312H19.6875C20.2916 9.95312 20.7812 10.4428 20.7812 11.0469Z"
      fill={main}
    />
    <path
      d="M37.3014 32.3121C36.9518 32.3774 36.6088 32.4052 36.2724 32.3954C35.9392 32.3888 35.6403 32.3284 35.3757 32.2141C35.1111 32.0965 34.9102 31.9119 34.773 31.6604C34.6521 31.4317 34.5884 31.1981 34.5819 30.9597C34.5753 30.7212 34.5721 30.4517 34.5721 30.1512V27.9461H33.6117V27.02H34.5721V25.55H35.7481V27.02H37.3014V27.9461H35.7481V30.0826C35.7481 30.2949 35.7497 30.4811 35.753 30.6412C35.7595 30.8012 35.7938 30.9319 35.8559 31.0332C35.9735 31.2292 36.1613 31.3386 36.4194 31.3615C36.6774 31.3843 36.9714 31.3713 37.3014 31.3223V32.3121Z"
      fill={gray2}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.0007 32.4591C15.4649 32.4591 14.9945 32.3431 14.5895 32.1112C14.1844 31.8792 13.8675 31.5575 13.6389 31.1459C13.4135 30.7343 13.3008 30.2606 13.3008 29.7249C13.3008 29.1467 13.4118 28.6452 13.634 28.2205C13.8561 27.7926 14.1648 27.461 14.5601 27.2258C14.9553 26.9906 15.4127 26.873 15.9321 26.873C16.4809 26.873 16.9464 27.0021 17.3286 27.2601C17.714 27.5149 17.9999 27.8759 18.1861 28.343C18.3723 28.8102 18.4425 29.3606 18.3968 29.9944H14.5665C14.6062 30.3743 14.7184 30.6798 14.9031 30.9107C15.1383 31.2047 15.4813 31.3517 15.9321 31.3517C16.2228 31.3517 16.4727 31.288 16.6818 31.1606C16.8941 31.0299 17.0574 30.8421 17.1718 30.5971L18.338 30.9499C18.1354 31.4268 17.8218 31.7976 17.3972 32.0622C16.9758 32.3268 16.5103 32.4591 16.0007 32.4591ZM16.9219 28.3038C17.0634 28.4918 17.1555 28.758 17.1982 29.1026H14.5954C14.6494 28.797 14.752 28.5487 14.9031 28.3577C15.1383 28.0572 15.4976 27.9069 15.9811 27.9069C16.409 27.9069 16.7226 28.0392 16.9219 28.3038Z"
      fill={gray2}
    />
    <path
      d="M26.4123 27.4169C26.7259 27.7665 26.8827 28.2271 26.8827 28.7988V32.3121H25.7018V29.0977C25.7018 28.735 25.612 28.4525 25.4323 28.2499C25.2527 28.0441 25.0093 27.9412 24.7022 27.9412C24.5128 27.9412 24.3429 27.9853 24.1926 28.0735C24.0424 28.1585 23.9231 28.2859 23.8349 28.4557C23.7467 28.6223 23.7026 28.8216 23.7026 29.0536V32.3121H22.5266V29.1123C22.5266 28.7432 22.4368 28.4557 22.2571 28.2499C22.0775 28.0441 21.8325 27.9412 21.5221 27.9412C21.2216 27.9412 20.9799 28.0441 20.7969 28.2499C20.6173 28.4557 20.5274 28.7236 20.5274 29.0536V32.3121H19.3367V27.02H20.3804V27.5936C20.5319 27.4036 20.7181 27.2484 20.939 27.1278C21.2298 26.9678 21.5548 26.8877 21.9141 26.8877C22.3094 26.8877 22.6393 26.9727 22.9039 27.1425C23.141 27.29 23.3243 27.486 23.4539 27.7307C23.6203 27.4731 23.8388 27.2705 24.1093 27.1229C24.3968 26.9661 24.7169 26.8877 25.0697 26.8877C25.6545 26.8877 26.102 27.0641 26.4123 27.4169Z"
      fill={gray2}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M29.4054 32.0916C29.7549 32.3366 30.1878 32.4591 30.7039 32.4591C31.1906 32.4591 31.6153 32.3366 31.9779 32.0916C32.3405 31.8466 32.6214 31.5134 32.8207 31.092C33.02 30.6706 33.1196 30.1953 33.1196 29.6661C33.1196 29.1303 33.0183 28.6534 32.8158 28.2352C32.6133 27.8138 32.3274 27.4823 31.9583 27.2405C31.5924 26.9955 31.1596 26.873 30.6598 26.873C30.1567 26.873 29.7337 26.9955 29.3907 27.2405C29.322 27.2894 29.2567 27.3419 29.1947 27.3981V27.02H28.151V34.6641H29.3417V32.0452C29.3626 32.061 29.3838 32.0764 29.4054 32.0916ZM29.1947 29.6661C29.1947 29.3198 29.2388 29.0176 29.327 28.7595C29.4185 28.4982 29.5589 28.2957 29.7484 28.1519C29.9379 28.0049 30.1829 27.9314 30.4834 27.9314C30.8003 27.9314 31.0616 28.0098 31.2674 28.1666C31.4732 28.3202 31.6251 28.5292 31.7231 28.7938C31.8211 29.0552 31.8701 29.3459 31.8701 29.6661C31.8701 29.9895 31.8211 30.2835 31.7231 30.5481C31.6284 30.8094 31.4814 31.0168 31.2821 31.1704C31.0828 31.3239 30.8297 31.4007 30.5226 31.4007C30.2057 31.4007 29.9493 31.3288 29.7533 31.1851C29.5606 31.0381 29.4185 30.8339 29.327 30.5726C29.2388 30.3112 29.1947 30.0091 29.1947 29.6661Z"
      fill={gray2}
    />
    <path
      d="M39.8618 32.2752L38.9916 34.6641H40.099L43.0488 27.02H41.8679L40.43 30.8071L38.9524 27.02H37.7225L39.8618 32.2752Z"
      fill={gray2}
    />
    <path
      d="M49.2188 10.5C49.2188 11.1041 48.7291 11.5938 48.125 11.5938C47.5209 11.5938 47.0312 11.1041 47.0312 10.5C47.0312 9.89592 47.5209 9.40625 48.125 9.40625C48.7291 9.40625 49.2188 9.89592 49.2188 10.5ZM43.75 9.40625C43.1459 9.40625 42.6562 9.89592 42.6562 10.5C42.6562 11.1041 43.1459 11.5938 43.75 11.5938C44.3541 11.5938 44.8438 11.1041 44.8438 10.5C44.8438 9.89592 44.3541 9.40625 43.75 9.40625ZM39.375 9.40625C38.7709 9.40625 38.2812 9.89592 38.2812 10.5C38.2812 11.1041 38.7709 11.5938 39.375 11.5938C39.9791 11.5938 40.4688 11.1041 40.4688 10.5C40.4688 9.89592 39.9791 9.40625 39.375 9.40625Z"
      fill={light}
      fillOpacity="0.5"
    />
    <defs>
      <linearGradient
        id="paint0_linear_10146_6013"
        x1="27.9121"
        y1="12.25"
        x2="27.9121"
        y2="48.3438"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopOpacity="0" />
        <stop offset="1" />
      </linearGradient>
    </defs>
  </svg>
)
