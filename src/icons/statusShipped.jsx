import React from 'react'

export const statusShipped = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
      status: { success },
    },
  },
}) => (
  <svg width={width || 18} height={height || 18} viewBox="0 0 18 18" fill={fill || light}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.8938 6.50625L15.9383 5.27339L14.4407 2.28752C14.3563 2.11877 14.1813 2.0094 13.9907 2.0094H11.8407V1.50313C11.8407 1.225 11.6157 1 11.3376 1H2.93135C2.65322 1 2.42822 1.225 2.42822 1.50313C2.42822 1.78125 2.65322 2.00625 2.93135 2.00625H10.8313V5.53438C10.8313 5.8125 11.0563 6.0375 11.3345 6.0375H15.2563L15.9907 6.98438V9.58438H15.0396C14.82 8.71039 14.0274 8.05933 13.0845 8.05933C12.1404 8.05933 11.3471 8.71196 11.1285 9.58752H8.14979C7.93123 8.71196 7.13793 8.05933 6.19385 8.05933C5.24976 8.05933 4.45646 8.71196 4.23791 9.58752H2.93135C2.65322 9.58752 2.42822 9.81252 2.42822 10.0906C2.42822 10.3688 2.65322 10.5938 2.93135 10.5938H4.24622C4.47635 11.4535 5.2637 12.0906 6.19385 12.0906C7.12286 12.0906 7.90943 11.455 8.14063 10.5969H11.1377C11.3689 11.455 12.1555 12.0906 13.0845 12.0906C14.0184 12.0906 14.8047 11.4519 15.0331 10.5906H16.497C16.7751 10.5906 17.0001 10.3656 17.0001 10.0875V6.81563C17.0001 6.70312 16.9626 6.59375 16.8938 6.50625ZM13.6782 3.01877L14.6906 5.03438H11.8407V3.01877H13.6782ZM6.19385 11.0843C5.6376 11.0843 5.18447 10.6312 5.18447 10.075C5.18447 9.5187 5.6376 9.06558 6.19385 9.06558C6.7501 9.06558 7.20322 9.5187 7.20322 10.075C7.20322 10.6312 6.7501 11.0843 6.19385 11.0843ZM13.0845 11.0843C12.5282 11.0843 12.0751 10.6312 12.0751 10.075C12.0751 9.5187 12.5282 9.06558 13.0845 9.06558C13.6407 9.06558 14.0938 9.5187 14.0938 10.075C14.0938 10.6312 13.6407 11.0843 13.0845 11.0843ZM2.32832 7.03442H4.64708C4.9252 7.03442 5.1502 7.25943 5.1502 7.53756C5.1502 7.81568 4.9252 8.04069 4.64708 8.04069H2.32832C2.0502 8.04069 1.8252 7.81568 1.8252 7.53756C1.8252 7.25943 2.0502 7.03442 2.32832 7.03442ZM5.80625 5.05005H1.50313C1.225 5.05005 1 5.27505 1 5.55317C1 5.8313 1.225 6.0563 1.50313 6.0563H5.80625C6.08437 6.0563 6.30937 5.8313 6.30937 5.55317C6.30937 5.27505 6.08437 5.05005 5.80625 5.05005ZM2.32832 3.06873H6.63145C6.90957 3.06873 7.13457 3.29373 7.13457 3.57185C7.13457 3.84998 6.90957 4.07498 6.63145 4.07498H2.32832C2.0502 4.07498 1.8252 3.84998 1.8252 3.57185C1.8252 3.29373 2.0502 3.06873 2.32832 3.06873Z"
      fill={fill || success}
    />
    <path
      d="M15.9383 5.27339L15.7148 5.38547L15.7258 5.40727L15.7407 5.42654L15.9383 5.27339ZM16.8938 6.50625L16.6962 6.6594L16.6973 6.66071L16.8938 6.50625ZM14.4407 2.28752L14.2171 2.39933L14.2173 2.39961L14.4407 2.28752ZM11.8407 2.0094H11.5907V2.2594H11.8407V2.0094ZM10.8313 2.00625H11.0813V1.75625H10.8313V2.00625ZM15.2563 6.0375L15.4539 5.88429L15.3788 5.7875H15.2563V6.0375ZM15.9907 6.98438H16.2407V6.89879L16.1883 6.83116L15.9907 6.98438ZM15.9907 9.58438V9.83438H16.2407V9.58438H15.9907ZM15.0396 9.58438L14.7972 9.64532L14.8447 9.83438H15.0396V9.58438ZM11.1285 9.58752V9.83752H11.3238L11.3711 9.64807L11.1285 9.58752ZM8.14979 9.58752L7.90723 9.64807L7.95452 9.83752H8.14979V9.58752ZM4.23791 9.58752V9.83752H4.43318L4.48047 9.64807L4.23791 9.58752ZM4.24622 10.5938L4.48772 10.5291L4.4381 10.3438H4.24622V10.5938ZM8.14063 10.5969V10.3469H7.94907L7.89924 10.5319L8.14063 10.5969ZM11.1377 10.5969L11.3791 10.5319L11.3292 10.3469H11.1377V10.5969ZM15.0331 10.5906V10.3406H14.8408L14.7915 10.5265L15.0331 10.5906ZM14.6906 5.03438V5.28438H15.096L14.914 4.92216L14.6906 5.03438ZM13.6782 3.01877L13.9016 2.90656L13.8324 2.76877H13.6782V3.01877ZM11.8407 5.03438H11.5907V5.28438H11.8407V5.03438ZM11.8407 3.01877V2.76877H11.5907V3.01877H11.8407ZM15.7407 5.42654L16.6962 6.6594L17.0914 6.3531L16.1359 5.12024L15.7407 5.42654ZM14.2173 2.39961L15.7148 5.38547L16.1618 5.16131L14.6642 2.17544L14.2173 2.39961ZM13.9907 2.2594C14.0861 2.2594 14.1748 2.31477 14.2171 2.39933L14.6643 2.17572C14.5379 1.92277 14.2766 1.7594 13.9907 1.7594V2.2594ZM11.8407 2.2594H13.9907V1.7594H11.8407V2.2594ZM11.5907 1.50313V2.0094H12.0907V1.50313H11.5907ZM11.3376 1.25C11.4777 1.25 11.5907 1.36307 11.5907 1.50313H12.0907C12.0907 1.08693 11.7538 0.75 11.3376 0.75V1.25ZM2.93135 1.25H11.3376V0.75H2.93135V1.25ZM2.67822 1.50313C2.67822 1.36307 2.79129 1.25 2.93135 1.25V0.75C2.51515 0.75 2.17822 1.08693 2.17822 1.50313H2.67822ZM2.93135 1.75625C2.79129 1.75625 2.67822 1.64318 2.67822 1.50313H2.17822C2.17822 1.91932 2.51515 2.25625 2.93135 2.25625V1.75625ZM10.8313 1.75625H2.93135V2.25625H10.8313V1.75625ZM11.0813 5.53438V2.00625H10.5813V5.53438H11.0813ZM11.3345 5.7875C11.1944 5.7875 11.0813 5.67443 11.0813 5.53438H10.5813C10.5813 5.95057 10.9183 6.2875 11.3345 6.2875V5.7875ZM15.2563 5.7875H11.3345V6.2875H15.2563V5.7875ZM16.1883 6.83116L15.4539 5.88429L15.0588 6.19071L15.7932 7.13759L16.1883 6.83116ZM16.2407 9.58438V6.98438H15.7407V9.58438H16.2407ZM15.0396 9.83438H15.9907V9.33438H15.0396V9.83438ZM13.0845 8.30933C13.9096 8.30933 14.6046 8.87936 14.7972 9.64532L15.2821 9.52344C15.0353 8.54142 14.1452 7.80933 13.0845 7.80933V8.30933ZM11.3711 9.64807C11.5626 8.88074 12.2583 8.30933 13.0845 8.30933V7.80933C12.0225 7.80933 11.1315 8.54318 10.886 9.52698L11.3711 9.64807ZM8.14979 9.83752H11.1285V9.33752H8.14979V9.83752ZM6.19385 8.30933C7.02001 8.30933 7.71569 8.88074 7.90723 9.64807L8.39234 9.52698C8.14677 8.54318 7.25585 7.80933 6.19385 7.80933V8.30933ZM4.48047 9.64807C4.672 8.88074 5.36768 8.30933 6.19385 8.30933V7.80933C5.13185 7.80933 4.24092 8.54318 3.99535 9.52698L4.48047 9.64807ZM2.93135 9.83752H4.23791V9.33752H2.93135V9.83752ZM2.67822 10.0906C2.67822 9.9506 2.79129 9.83752 2.93135 9.83752V9.33752C2.51515 9.33752 2.17822 9.67445 2.17822 10.0906H2.67822ZM2.93135 10.3438C2.79129 10.3438 2.67822 10.2307 2.67822 10.0906H2.17822C2.17822 10.5068 2.51515 10.8438 2.93135 10.8438V10.3438ZM4.24622 10.3438H2.93135V10.8438H4.24622V10.3438ZM6.19385 11.8406C5.3802 11.8406 4.68945 11.2828 4.48772 10.5291L4.00472 10.6584C4.26325 11.6242 5.14721 12.3406 6.19385 12.3406V11.8406ZM7.89924 10.5319C7.69657 11.2841 7.00651 11.8406 6.19385 11.8406V12.3406C7.23922 12.3406 8.1223 11.626 8.38203 10.6619L7.89924 10.5319ZM11.1377 10.3469H8.14063V10.8469H11.1377V10.3469ZM13.0845 11.8406C12.2718 11.8406 11.5818 11.2841 11.3791 10.5319L10.8963 10.6619C11.156 11.626 12.0391 12.3406 13.0845 12.3406V11.8406ZM14.7915 10.5265C14.5913 11.2815 13.9017 11.8406 13.0845 11.8406V12.3406C14.135 12.3406 15.0182 11.6224 15.2748 10.6547L14.7915 10.5265ZM16.497 10.3406H15.0331V10.8406H16.497V10.3406ZM16.7501 10.0875C16.7501 10.2276 16.637 10.3406 16.497 10.3406V10.8406C16.9132 10.8406 17.2501 10.5037 17.2501 10.0875H16.7501ZM16.7501 6.81563V10.0875H17.2501V6.81563H16.7501ZM16.6973 6.66071C16.7309 6.70352 16.7501 6.75832 16.7501 6.81563H17.2501C17.2501 6.64793 17.1943 6.48398 17.0904 6.35179L16.6973 6.66071ZM14.914 4.92216L13.9016 2.90656L13.4548 3.13099L14.4672 5.14659L14.914 4.92216ZM11.8407 5.28438H14.6906V4.78438H11.8407V5.28438ZM11.5907 3.01877V5.03438H12.0907V3.01877H11.5907ZM13.6782 2.76877H11.8407V3.26877H13.6782V2.76877ZM4.93447 10.075C4.93447 10.7693 5.49953 11.3343 6.19385 11.3343V10.8343C5.77567 10.8343 5.43447 10.4931 5.43447 10.075H4.93447ZM6.19385 8.81558C5.49953 8.81558 4.93447 9.38063 4.93447 10.075H5.43447C5.43447 9.65677 5.77567 9.31558 6.19385 9.31558V8.81558ZM7.45322 10.075C7.45322 9.38063 6.88817 8.81558 6.19385 8.81558V9.31558C6.61203 9.31558 6.95322 9.65677 6.95322 10.075H7.45322ZM6.19385 11.3343C6.88817 11.3343 7.45322 10.7693 7.45322 10.075H6.95322C6.95322 10.4931 6.61203 10.8343 6.19385 10.8343V11.3343ZM11.8251 10.075C11.8251 10.7693 12.3902 11.3343 13.0845 11.3343V10.8343C12.6663 10.8343 12.3251 10.4931 12.3251 10.075H11.8251ZM13.0845 8.81558C12.3902 8.81558 11.8251 9.38063 11.8251 10.075H12.3251C12.3251 9.65677 12.6663 9.31558 13.0845 9.31558V8.81558ZM14.3438 10.075C14.3438 9.38063 13.7788 8.81558 13.0845 8.81558V9.31558C13.5027 9.31558 13.8438 9.65677 13.8438 10.075H14.3438ZM13.0845 11.3343C13.7788 11.3343 14.3438 10.7693 14.3438 10.075H13.8438C13.8438 10.4931 13.5027 10.8343 13.0845 10.8343V11.3343ZM4.64708 6.78442H2.32832V7.28442H4.64708V6.78442ZM5.4002 7.53756C5.4002 7.12136 5.06328 6.78442 4.64708 6.78442V7.28442C4.78713 7.28442 4.9002 7.3975 4.9002 7.53756H5.4002ZM4.64708 8.29069C5.06328 8.29069 5.4002 7.95375 5.4002 7.53756H4.9002C4.9002 7.67762 4.78713 7.79069 4.64708 7.79069V8.29069ZM2.32832 8.29069H4.64708V7.79069H2.32832V8.29069ZM1.5752 7.53756C1.5752 7.95375 1.91212 8.29069 2.32832 8.29069V7.79069C2.18827 7.79069 2.0752 7.67762 2.0752 7.53756H1.5752ZM2.32832 6.78442C1.91212 6.78442 1.5752 7.12136 1.5752 7.53756H2.0752C2.0752 7.3975 2.18827 7.28442 2.32832 7.28442V6.78442ZM1.50313 5.30005H5.80625V4.80005H1.50313V5.30005ZM1.25 5.55317C1.25 5.41312 1.36307 5.30005 1.50313 5.30005V4.80005C1.08693 4.80005 0.75 5.13698 0.75 5.55317H1.25ZM1.50313 5.8063C1.36307 5.8063 1.25 5.69323 1.25 5.55317H0.75C0.75 5.96937 1.08693 6.3063 1.50313 6.3063V5.8063ZM5.80625 5.8063H1.50313V6.3063H5.80625V5.8063ZM6.05937 5.55317C6.05937 5.69323 5.9463 5.8063 5.80625 5.8063V6.3063C6.22245 6.3063 6.55937 5.96937 6.55937 5.55317H6.05937ZM5.80625 5.30005C5.9463 5.30005 6.05937 5.41312 6.05937 5.55317H6.55937C6.55937 5.13698 6.22245 4.80005 5.80625 4.80005V5.30005ZM6.63145 2.81873H2.32832V3.31873H6.63145V2.81873ZM7.38457 3.57185C7.38457 3.15565 7.04764 2.81873 6.63145 2.81873V3.31873C6.7715 3.31873 6.88457 3.4318 6.88457 3.57185H7.38457ZM6.63145 4.32498C7.04764 4.32498 7.38457 3.98805 7.38457 3.57185H6.88457C6.88457 3.7119 6.7715 3.82498 6.63145 3.82498V4.32498ZM2.32832 4.32498H6.63145V3.82498H2.32832V4.32498ZM1.5752 3.57185C1.5752 3.98805 1.91212 4.32498 2.32832 4.32498V3.82498C2.18827 3.82498 2.0752 3.7119 2.0752 3.57185H1.5752ZM2.32832 2.81873C1.91212 2.81873 1.5752 3.15565 1.5752 3.57185H2.0752C2.0752 3.4318 2.18827 3.31873 2.32832 3.31873V2.81873Z"
      fill={fill || success}
    />
  </svg>
)
