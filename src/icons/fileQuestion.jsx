import React from 'react'

export const fileQuestion = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M9.99985 17.9628H4.99984C3.89527 17.9628 2.99984 17.0674 2.99985 15.9628L2.99992 3.96288C2.99993 2.85831 3.89536 1.96289 4.99992 1.96289H14.0001C15.1047 1.96289 16.0001 2.85832 16.0001 3.96289V7.46289M13.0002 13.3442C13.0002 12.2636 13.8956 11.3877 15.0002 11.3877C16.1047 11.3877 17.0002 12.2636 17.0002 13.3442C17.0002 14.4248 16.1047 15.3008 15.0002 15.3008M15.0002 18.038V17.9628M6.50016 5.96289H12.5002M6.50016 8.96289H12.5002M6.50016 11.9629H9.50016"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <circle cx="15.4" cy="17.9629" r="1" fill={fill} />
  </svg>
)
