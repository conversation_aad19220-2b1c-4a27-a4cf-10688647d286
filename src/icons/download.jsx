import React from 'react'

export const download = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 15} viewBox="0 0 16 16">
    <path
      d="M17 7.31583L17 4.22759C17 3.75957 16.8156 3.31071 16.4874 2.97976C16.1592 2.64881 15.7141 2.46289 15.25 2.46289L4.75 2.46289C4.28587 2.46289 3.84075 2.64881 3.51257 2.97976C3.18438 3.3107 3 3.75957 3 4.22759L3 7.31583M9.99902 17.5352L9.99903 7.53515M9.99903 7.53515L5.99903 11.3561M9.99903 7.53515L13.999 11.3561"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
