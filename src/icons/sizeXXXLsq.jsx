import React from 'react'

export const sizeXXXLsq = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.96751 13.6582L4.68054 11.8127L3.41642 13.6582H2L3.97233 10.9544L2.099 8.34177H3.50019L4.72623 10.0658L5.92943 8.34177H7.82932L9.05318 10.0628L10.2542 8.34177H12.1541L13.3802 10.0658L14.5834 8.34177H15.916L14.0579 10.9089L16.0455 13.6582H14.6214L13.3345 11.8127L12.0704 13.6582H10.2923L9.00753 11.8158L7.74555 13.6582H5.96751ZM5.40398 10.9089L6.84097 8.92357L8.29714 10.9544L6.86058 12.9238L5.40398 10.9089ZM9.73311 10.9089L11.1701 8.92357L12.6263 10.9544L11.1897 12.9238L9.73311 10.9089Z"
      fill={fill || dark}
    />
    <path d="M17.3272 8.34177H16.0935V13.6582H20.0001V12.6557H17.3272V8.34177Z" fill={fill || dark} />
  </svg>
)
