import React from 'react'

export const layers = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M14.5 8.18525L18 9.97927L10 14.0799L2 9.97927L5.56397 8.15246M14.5 12.1054L18 13.8994L10 18L2 13.8994L5.56397 12.0726M10 2L18 6.10062L10 10.2012L2 6.10062L10 2Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
