import React from 'react'

export const warning = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || '16'} height={height || '17'} viewBox="0 0 16 17" fill="none">
    <path
      d="M8.0001 8.49961L8.0001 11.6996M8.0001 6.12773V6.09961M1.6001 8.49961C1.6001 4.96499 4.46548 2.09961 8.0001 2.09961C11.5347 2.09961 14.4001 4.96499 14.4001 8.49961C14.4001 12.0342 11.5347 14.8996 8.0001 14.8996C4.46547 14.8996 1.6001 12.0342 1.6001 8.49961Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || '1.4'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
