import React from 'react'

export const globe = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M10 20c-5.514 0-10-4.486-10-10s4.486-10 10-10c5.514 0 9.999 4.486 9.999 10s-4.485 10-9.999 10zM10 0.84c-5.051 0-9.161 4.109-9.161 9.16s4.11 9.16 9.161 9.16c5.050 0 9.159-4.109 9.159-9.16s-4.109-9.16-9.159-9.16z"
      stroke="none"
      fill={fill || stroke || dark}
    />
    <path d="M0.56 9.58h18.88v0.84h-18.88v-0.84z" fill={fill || stroke || dark} />
    <path
      d="M10.227 6.053c-4.74 0-8.278-1.043-8.498-1.11l0.243-0.804c0.080 0.024 8.055 2.372 16.019-0l0.239 0.805c-2.793 0.832-5.559 1.109-8.003 1.109z"
      fill={fill || stroke || dark}
    />
    <path
      d="M1.99 15.648l-0.24-0.805c8.212-2.444 16.167-0.1 16.502 0l-0.243 0.805c-0.080-0.024-8.055-2.371-16.019 0z"
      fill={fill || stroke || dark}
    />
    <path
      d="M6.763 19.117c-4.138-9.689-0.567-18.279-0.53-18.364l0.773 0.329c-0.036 0.082-3.463 8.357 0.53 17.705l-0.773 0.33z"
      fill={fill || stroke || dark}
    />
    <path
      d="M13.172 19.117l-0.773-0.33c3.993-9.348 0.565-17.622 0.53-17.705l0.773-0.329c0.036 0.085 3.608 8.675-0.53 18.364z"
      fill={fill || stroke || dark}
    />
    <path d="M9.58 0.56h0.839v19.227h-0.839v-19.227z" fill={fill || stroke || dark} />
  </svg>
)
