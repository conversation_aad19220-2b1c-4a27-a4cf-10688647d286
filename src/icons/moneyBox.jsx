import React from 'react'

export const moneyBox = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M16.8632 11.0667V16C16.8632 16.5523 16.4155 17 15.8632 17H4.1299C3.57762 17 3.1299 16.5523 3.1299 16V11.0667M16.8632 11.0667H3.1299M16.8632 11.0667L16.8632 6.80001C16.8632 6.24772 16.4155 5.80001 15.8632 5.80001L12.1299 5.80002M3.1299 11.0667L3.12989 6.80001C3.12988 6.24773 3.5776 5.80001 4.12989 5.80001L7.86321 5.80002M7.3299 7.93334H12.6632M12.1299 5.80002C12.1299 4.62259 11.174 3.66668 9.99655 3.66668C8.81912 3.66668 7.86321 4.62259 7.86321 5.80002M12.1299 5.80002C12.1299 6.97745 11.174 7.93333 9.99655 7.93333C8.81912 7.93333 7.86321 6.97745 7.86321 5.80002M9.99655 1.53334V1M7.86322 2.06667V1.53336M12.1299 2.06667V1.53336"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
