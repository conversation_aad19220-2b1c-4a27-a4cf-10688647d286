import React from 'react'

export const file = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M16.3158 8.7372V6.36878C16.3158 5.8165 15.8681 5.36878 15.3158 5.36878H9.99316C9.72794 5.36878 9.47359
    5.26342 9.28605 5.07589L8.18763 3.97746C8.00009 3.78993 7.74574 3.68457 7.48052 3.68457H3C2.44772 3.68457
    2 4.13228 2 4.68457V15.3162C2 15.8684 2.44772 16.3162 3 16.3162H3.81347C4.20716 16.3162 4.52632 15.997
    4.52632 15.6033V15.6033C4.52632 15.5178 4.54169 15.433 4.5717 15.353L6.8093 9.38608C6.95567 8.99578
    7.32879 8.7372 7.74563 8.7372H16.6126C17.2951 8.7372 17.7771 9.4059 17.5613 10.0534L15.7016
    15.6324C15.5655 16.0407 15.1834 16.3162 14.7529 16.3162H3.68421"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
