import React from 'react'

export const bulb = ({ fill, width, height }) => (
  <svg width={width || 16} height={height || 22} viewBox="0 0 16 22">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 8C0 10.9611 1.60879 13.5465 4 14.9297V14.9999C4 17.2091 5.79086 18.9999 8 18.9999C10.2091 18.9999 12 17.2091 12 14.9999V14.9297C14.3912 13.5465 16 10.9611 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8ZM12 12.4722C13.2275 11.3736 14 9.777 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 9.777 2.7725 11.3736 4 12.4722L6 12.4713V15C6.00003 16.1045 6.89545 16.9999 8 16.9999C9.10455 16.9999 9.99997 16.1045 10 14.9999V12.4713L12 12.4722Z"
      fill={fill || 'white'}
    />
    <path
      d="M6 20V19.9936C6.58835 20.3339 7.27143 20.5287 8 20.5287C8.72857 20.5287 9.41165 20.3339 10 19.9936V20C10 21.1046 9.10457 22 8 22C6.89543 22 6 21.1046 6 20Z"
      fill={fill || 'white'}
    />
  </svg>
)
