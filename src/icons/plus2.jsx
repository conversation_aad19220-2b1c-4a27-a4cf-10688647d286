import React from 'react'

export const plus2 = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray5 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M10 4L10 16M16 10L4 10"
      stroke={stroke || fill || gray5}
      strokeLinecap="round"
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
