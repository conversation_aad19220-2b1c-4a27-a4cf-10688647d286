import React from 'react'

export const tShirtSlide = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray6 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M5.45458 6.63638H6.5182C6.68389 6.63638 6.8182 6.7707 6.8182 6.93638V12.7727C6.8182 12.8982 6.92002 13 7.04548 13H12.9545C13.08 13 13.1818 12.8982 13.1818 12.7727V6.93639C13.1818 6.7707 13.3161 6.63638 13.4818 6.63638H14.5454C14.6613 6.63638 14.7584 6.54934 14.7713 6.43411L14.9986 4.38866C15.0104 4.28207 14.9463 4.18185 14.8447 4.14798L11.4357 3.01162C11.3482 2.98253 11.2514 3.00912 11.1911 3.07935C10.6002 3.80321 9.4 3.80276 8.8091 3.07935C8.74887 3.00935 8.65228 2.98253 8.56456 3.01162L5.15549 4.14798C5.05367 4.18185 4.98958 4.2823 5.0014 4.38866L5.22867 6.43411C5.24162 6.54934 5.33867 6.63638 5.45458 6.63638Z"
      stroke={fill || stroke || gray6}
    />
    <path
      d="M3.5 12L2 10.5L3.5 9M16.5 9L18 10.5L16.5 12"
      stroke={fill || stroke || gray6}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M7 16C7 16.5523 6.55228 17 6 17C5.44772 17 5 16.5523 5 16C5 15.4477 5.44772 15 6 15C6.55228 15 7 15.4477 7 16Z"
      stroke={fill || stroke || gray6}
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M11 16C11 16.5523 10.5523 17 10 17C9.44772 17 9 16.5523 9 16C9 15.4477 9.44772 15 10 15C10.5523 15 11 15.4477 11 16Z"
      stroke={fill || stroke || gray6}
      strokeWidth={strokeWidth || 1}
    />
    <path
      d="M15 16C15 16.5523 14.5523 17 14 17C13.4477 17 13 16.5523 13 16C13 15.4477 13.4477 15 14 15C14.5523 15 15 15.4477 15 16Z"
      stroke={fill || stroke || gray6}
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
