import React from 'react'

export const page404 = ({
  width,
  height,
  text,
  theme: {
    color: {
      primary: { main, lightest, light, blackest },
      general: { gray3, dark },
      secondary: { main: secondMain, light: secondLight, dark: secondDark, darkest, darker, primary },
    },
  },
}) => (
  <svg width={width || 540} height={height || 518} viewBox="0 0 540 518">
    <g clipPath="url(#clip0)">
      <path
        d="M289.999 20.4426C263.209 20.1887 100.689 17.6493 95.9914 62.977C91.4206 106.273 154.397 90.4022 155.032 109.701C155.667 129.889 143.732 135.73 110.212 134.46C95.7375 133.952 49.9019 139.031 49.1401 167.98C48.2513 196.675 99.4196 192.993 96.8802 216.863C95.3566 244.034 56.5042 249.875 41.268 257.112C24.6352 265.873 -5.96421 280.093 1.01906 310.946C10.2877 346.624 69.0741 340.276 91.1666 344.593C105.895 347.005 153 351.195 146.271 382.81C142.716 401.856 104.117 402.871 111.482 427.376C118.973 450.231 186.266 454.675 199.852 455.436C256.479 458.484 380.782 463.562 436.14 440.2C456.455 431.693 474.484 421.409 472.326 390.936C471.31 363.638 456.582 359.702 481.34 338.625C496.958 325.04 526.922 326.183 525.145 286.568C524.129 265.238 469.532 256.35 470.802 234.003C472.707 198.96 539.746 228.671 540 166.583C540.127 127.858 418.237 149.569 419.507 111.225C417.856 95.6079 444.647 89.0055 443.504 71.8648C444.266 44.1857 412.778 23.8708 289.999 20.4426Z"
        fill={lightest}
      />
      <path
        d="M183.6 301.552C183.473 301.679 183.346 301.679 183.092 301.679C151.985 301.679 120.877 301.679 89.77 301.679C89.3891 301.679 88.8813 301.425 88.8813 300.917C87.8655 296.473 92.9443 293.045 96.4994 294.696C97.0073 292.41 99.1657 290.887 101.578 291.141C100.308 287.078 102.086 283.015 106.276 280.983C110.212 279.206 113.894 280.602 116.179 283.65C117.576 276.793 123.417 270.826 130.654 270.318C134.59 270.064 138.78 271.207 141.827 273.746C144.874 276.285 145.89 279.079 146.652 282.507C148.937 279.206 152.619 277.682 156.809 278.952C160.999 280.221 161.507 284.538 161.38 288.347C164.808 285.554 168.871 284.157 173.188 286.824C176.87 288.982 177.632 292.537 177.759 296.346C180.806 294.569 183.854 296.346 184.108 300.409C184.108 301.171 183.854 301.425 183.6 301.552Z"
        fill={light}
      />

      <path
        d="M444.012 108.052C444.012 125.955 492.514 129.256 495.053 108.052C497.592 86.8487 444.012 81.2621 444.012 108.052Z"
        fill={lightest}
      />
      <path
        d="M51.9331 383.318C51.9331 401.221 111.989 404.649 114.148 383.318C116.941 357.036 51.9331 351.957 51.9331 383.318Z"
        fill={lightest}
      />
      <path
        d="M402.112 484.258C402.112 505.462 473.342 509.525 476.008 484.258C479.182 453.024 402.112 447.183 402.112 484.258Z"
        fill={lightest}
      />
      <path
        d="M46.8545 109.322C46.8545 121.003 79.9932 123.288 81.136 109.322C82.6596 92.181 46.8545 88.8798 46.8545 109.322Z"
        fill={lightest}
      />

      <path
        d="M236.672 317.422C237.688 312.47 230.324 307.899 224.61 309.677C225.499 306.248 224.483 303.201 219.405 301.678C217.373 301.043 214.834 301.043 212.548 301.678C216.484 293.806 212.802 284.791 200.613 282.886C187.282 280.855 172.553 287.457 169.887 297.869C159.095 287.203 136.113 300.027 142.843 312.216C138.653 311.581 134.336 313.867 134.082 317.422C129.892 316.025 126.464 320.342 129.13 324.024C129.257 324.278 129.765 324.405 130.146 324.405C167.856 324.405 205.438 324.405 243.148 324.405C243.402 324.405 243.656 324.278 243.783 324.278C243.91 324.151 244.164 324.024 244.164 323.77C245.56 320.215 240.862 316.787 236.672 317.422Z"
        fill={main}
      />

      <path
        d="M237.815 401.729C238.196 399.571 235.022 397.539 232.483 398.301C232.864 396.778 232.483 395.381 230.197 394.746C229.308 394.492 228.166 394.492 227.15 394.746C228.928 391.318 227.277 387.255 221.817 386.493C215.977 385.604 209.374 388.525 208.232 393.095C203.407 388.398 193.249 393.984 196.297 399.444C194.392 399.19 192.488 400.206 192.488 401.729C190.583 401.094 189.186 402.999 190.329 404.65C190.456 404.777 190.583 404.904 190.71 404.904C207.343 404.904 223.976 404.904 240.609 404.904C240.736 404.904 240.863 404.904 240.863 404.777C240.99 404.777 240.99 404.65 240.99 404.523C241.751 402.872 239.72 401.348 237.815 401.729Z"
        fill={light}
      />

      <path
        d="M425.94 198.073C426.067 198.073 426.194 198.2 426.321 198.2C449.43 198.2 472.665 198.2 495.773 198.2C496.027 198.2 496.408 197.946 496.408 197.692C497.17 194.391 493.361 191.851 490.821 193.121C490.44 191.47 488.79 190.328 487.012 190.455C488.028 187.407 486.631 184.36 483.457 182.963C480.537 181.567 477.871 182.709 476.093 184.868C475.077 179.789 470.633 175.345 465.301 174.964C462.38 174.71 459.333 175.599 457.048 177.504C454.762 179.408 454 181.44 453.493 184.106C451.842 181.694 449.049 180.551 445.875 181.44C442.7 182.455 442.319 185.63 442.446 188.423C439.907 186.392 436.86 185.376 433.686 187.28C431.019 188.931 430.384 191.597 430.257 194.391C427.972 193.121 425.687 194.391 425.56 197.438C425.56 197.692 425.814 197.946 425.94 198.073Z"
        fill={main}
      />
      <path
        d="M258.511 73.39C258.638 73.39 258.638 73.5169 258.765 73.5169C274.382 73.5169 289.999 73.5169 305.743 73.5169C305.997 73.5169 306.124 73.3899 306.251 73.136C306.759 70.8506 304.219 69.2 302.442 69.9618C302.188 68.8191 301.045 68.0573 299.902 68.1842C300.537 66.1528 299.649 64.1213 297.49 63.1055C295.586 62.2167 293.681 62.9785 292.538 64.3752C291.903 60.9471 288.856 57.8998 285.301 57.6459C283.27 57.5189 281.238 58.0268 279.714 59.2965C278.191 60.5661 277.683 61.9628 277.302 63.7404C276.159 62.0898 274.255 61.328 272.223 61.9628C270.065 62.5976 269.811 64.7561 269.938 66.6606C268.287 65.264 266.256 64.6291 263.97 65.8988C262.193 67.0415 261.685 68.8191 261.685 70.7236C260.161 69.8348 258.638 70.7236 258.511 72.7551C258.257 73.136 258.257 73.263 258.511 73.39Z"
        fill={light}
      />

      <path
        d="M369.608 488.971C369.608 488.844 369.481 488.59 369.481 488.463C369.354 488.209 369.1 487.955 368.719 487.955C356.911 488.082 345.103 488.336 333.295 488.59C332.787 488.59 332.406 488.971 332.406 489.479C332.66 496.335 332.152 503.318 333.295 510.175C334.31 516.523 341.294 516.904 346.626 517.285C353.483 517.666 363.259 517.92 367.195 514.238C371.639 510.048 369.861 495.573 369.608 488.971Z"
        fill={secondMain}
      />

      <path
        d="M350.183 485.669C346.373 485.669 333.677 485.796 332.28 488.716C330.375 492.779 351.198 492.398 355.134 492.271C374.18 491.637 376.465 484.653 350.183 485.669Z"
        fill={secondMain}
      />
      <path
        d="M350.181 484.909C348.023 484.909 329.866 484.655 331.517 489.48C332.279 491.892 336.215 492.146 338.246 492.4C342.69 493.035 347.388 493.035 351.959 493.035C356.022 493.035 359.958 492.781 364.021 492.146C365.799 491.892 369.608 491.257 370.369 489.099C372.274 483.766 351.578 484.782 350.181 484.909C349.293 484.909 349.293 486.433 350.181 486.306C354.371 486.179 358.434 486.179 362.624 486.559C363.386 486.686 370.623 487.575 368.211 489.099C363.894 491.892 356.911 491.384 351.832 491.511C346.245 491.511 340.278 491.638 334.818 490.242C332.152 489.48 333.929 488.083 335.707 487.575C337.485 487.067 339.516 486.94 341.421 486.686C344.341 486.433 347.261 486.306 350.181 486.306C351.07 486.306 351.07 484.909 350.181 484.909Z"
        fill={blackest}
      />
      <path
        d="M369.862 445.421C369.608 459.768 369.101 474.116 368.974 488.463C368.974 489.225 370.116 489.225 370.116 488.463C370.37 474.116 370.878 459.768 371.005 445.421C371.005 444.659 369.862 444.659 369.862 445.421Z"
        fill={gray3}
      />
      <path
        d="M329.613 445.167C329.994 459.768 330.248 474.243 331.771 488.717C331.898 489.352 332.914 489.352 332.914 488.717C331.39 474.243 331.136 459.768 330.755 445.167C330.629 444.405 329.613 444.405 329.613 445.167Z"
        fill={gray3}
      />
      <path
        d="M349.293 447.58C350.563 462.181 349.039 476.909 350.309 491.638C350.309 492.273 351.451 492.399 351.451 491.638C350.182 477.036 351.705 462.308 350.436 447.58C350.309 446.945 349.166 446.945 349.293 447.58Z"
        fill={gray3}
      />

      <path
        d="M426.109 320.357C421.538 308.802 413.032 297.121 399.827 294.836C399.7 294.836 399.573 294.836 399.573 294.836C399.446 294.836 399.446 294.836 399.319 294.836C389.415 293.82 383.702 303.724 380.274 311.215C374.052 324.8 371.259 340.037 368.847 354.765C364.022 383.587 359.07 415.075 367.45 443.643C367.958 445.166 369.989 445.42 370.878 444.024C379.893 430.184 398.557 425.359 410.492 414.313C422.935 402.759 429.284 385.872 431.315 369.493C433.347 352.607 432.331 336.101 426.109 320.357Z"
        fill="url(#paint0_linear)"
      />
      <path
        d="M327.836 366.829C323.392 343.085 313.108 319.469 301.172 298.52C300.792 297.758 300.03 297.504 299.395 297.631C299.141 297.631 298.887 297.758 298.506 297.885C286.444 304.106 280.096 315.914 276.033 328.103C271.716 341.181 269.43 357.179 271.462 370.892C274.128 388.667 284.159 404.284 297.363 416.473C308.156 426.377 321.614 432.852 331.264 444.279C332.28 445.549 334.438 444.533 334.438 443.01C334.311 417.489 332.534 391.841 327.836 366.829Z"
        fill="url(#paint1_linear)"
      />
      <path
        d="M384.971 280.996C374.433 275.156 361.99 274.267 350.309 276.552C349.547 276.679 349.166 277.187 349.039 277.695C341.929 287.853 340.913 301.946 339.897 313.754C338.247 332.291 338.755 351.083 339.517 369.747C340.659 394.887 342.691 420.027 345.992 444.912C346.119 445.928 346.754 446.69 347.896 446.69C355.134 446.69 362.244 446.69 369.481 446.69C370.116 446.69 370.751 446.309 371.132 445.801C391.066 409.234 396.652 365.303 399.954 324.419C400.715 315.151 401.35 305.755 401.477 296.486C401.35 290.392 389.415 283.409 384.971 280.996Z"
        fill="url(#paint2_linear)"
      />
      <path
        d="M355.134 283.79C351.705 264.364 317.424 279.854 308.282 286.457C299.902 292.551 297.617 298.392 297.109 307.787C296.22 323.405 297.109 339.149 299.013 354.766C301.807 376.096 307.139 397.3 315.773 417.107C318.44 423.075 321.36 429.042 324.534 434.756C325.931 437.422 329.105 445.675 332.279 446.564C336.85 447.834 343.072 446.691 347.769 446.691C348.658 446.691 349.293 446.183 349.547 445.421C349.674 445.294 349.674 445.04 349.674 444.786C352.594 409.235 355.007 373.684 356.149 338.133C356.784 320.357 358.181 301.439 355.134 283.79Z"
        fill="url(#paint3_linear)"
      />

      <path
        d="M329.105 447.579C343.071 450.118 357.673 449.991 371.639 447.579C375.575 446.944 373.925 441.611 369.989 442.246C357.292 444.405 343.579 444.659 330.882 442.246C326.819 441.484 325.169 446.817 329.105 447.579Z"
        fill={secondMain}
      />

      <path
        d="M200.613 92.9415C200.613 92.8145 200.613 92.8145 200.613 92.6875C200.613 92.5606 200.486 92.4336 200.232 92.4336C195.154 92.5606 189.948 92.5606 184.869 92.6875C184.615 92.6875 184.488 92.8145 184.488 93.0684C184.615 95.9887 184.361 99.036 184.869 101.956C185.377 104.75 188.297 104.876 190.583 105.003C193.503 105.13 197.82 105.257 199.471 103.734C201.502 102.083 200.74 95.8617 200.613 92.9415Z"
        fill={secondMain}
      />
      <path
        d="M192.233 91.5459C190.583 91.5459 185.123 91.6729 184.488 92.8156C183.726 94.5932 192.614 94.4662 194.392 94.3392C202.645 94.0853 203.66 91.165 192.233 91.5459Z"
        fill={darker}
      />

      <path
        d="M192.233 91.1644C191.344 91.1644 183.345 91.0375 184.107 93.1959C184.488 94.2117 186.139 94.3386 187.028 94.4656C188.932 94.7195 190.964 94.7195 192.995 94.7195C194.773 94.7195 196.423 94.5926 198.201 94.3386C198.963 94.2117 200.613 93.9577 200.994 93.069C201.756 90.7835 192.868 91.1644 192.233 91.1644C191.852 91.1644 191.852 91.7993 192.233 91.7993C194.011 91.7993 195.788 91.7993 197.566 91.9262C197.82 91.9262 200.994 92.4341 199.978 93.069C198.074 94.3386 195.026 94.0847 192.868 94.0847C190.456 94.0847 187.916 94.2117 185.504 93.5768C184.361 93.3229 185.123 92.5611 185.885 92.4341C186.647 92.1802 187.535 92.1802 188.297 92.0532C189.567 91.9262 190.837 91.9262 192.106 91.9262C192.614 91.7993 192.614 91.1644 192.233 91.1644Z"
        fill={blackest}
      />

      <path
        d="M200.74 74.1509C200.613 80.3724 200.359 86.5938 200.359 92.8153C200.359 93.0692 200.867 93.0692 200.867 92.8153C200.994 86.5938 201.121 80.3724 201.248 74.1509C201.248 73.77 200.74 73.77 200.74 74.1509Z"
        fill={gray3}
      />
      <path
        d="M183.346 74.0244C183.6 80.3729 183.6 86.5943 184.234 92.8158C184.234 93.0697 184.742 93.0697 184.742 92.8158C184.108 86.5943 183.981 80.2459 183.854 74.0244C183.727 73.7705 183.346 73.7705 183.346 74.0244Z"
        fill={gray3}
      />
      <path
        d="M191.853 75.0401C192.36 81.3885 191.726 87.7369 192.233 94.0853C192.233 94.3393 192.741 94.3393 192.741 94.0853C192.233 87.7369 192.868 81.3885 192.36 75.0401C192.233 74.7861 191.853 74.7861 191.853 75.0401Z"
        fill={gray3}
      />
      <path
        d="M225.119 19.9357C223.087 14.857 219.532 9.9052 213.692 8.88945H213.565C213.565 8.88945 213.565 8.88945 213.438 8.88945C209.121 8.50855 206.708 12.6985 205.185 15.9997C202.518 21.8402 201.249 28.4426 200.233 34.791C198.201 47.2339 196.043 60.9465 199.598 73.2624C199.852 73.8973 200.741 74.0242 201.122 73.3894C205.058 67.4219 213.057 65.2634 218.262 60.5656C223.595 55.6138 226.388 48.2497 227.277 41.1394C228.293 33.9022 227.785 26.792 225.119 19.9357Z"
        fill="url(#paint4_linear)"
      />
      <path
        d="M182.584 40.1227C180.68 29.8383 176.236 19.5539 171.03 10.5391C170.903 10.2852 170.522 10.1582 170.268 10.1582C170.141 10.1582 170.014 10.1582 169.887 10.2852C164.682 12.9515 161.888 18.0302 160.111 23.3629C158.206 29.0765 157.19 35.9328 158.079 41.9003C159.222 49.6454 163.539 56.3747 169.252 61.5804C173.95 65.8973 179.791 68.6906 183.981 73.6424C184.489 74.1503 185.377 73.7694 185.377 73.1345C185.377 62.0883 184.616 50.9151 182.584 40.1227Z"
        fill="url(#paint5_linear)"
      />
      <path
        d="M207.342 2.9226C202.771 0.383233 197.438 0.00232774 192.36 1.01808C192.106 1.01808 191.852 1.27201 191.852 1.52595C188.804 5.84287 188.297 12.0643 187.916 17.1431C187.154 25.1421 187.408 33.395 187.789 41.394C188.297 52.3133 189.185 63.1056 190.582 73.8979C190.582 74.2788 190.963 74.6598 191.344 74.6598C194.518 74.6598 197.565 74.6598 200.739 74.6598C200.993 74.6598 201.247 74.5328 201.501 74.2788C210.135 58.4078 212.548 39.3625 213.944 21.7139C214.325 17.6509 214.579 13.5879 214.579 9.65193C214.452 6.98559 209.246 3.93835 207.342 2.9226Z"
        fill="url(#paint6_linear)"
      />
      <path
        d="M194.392 4.0645C192.868 -4.31542 178.013 2.41391 174.077 5.20721C170.395 7.87355 169.506 10.4129 169.252 14.4759C168.871 21.2052 169.252 28.0615 170.141 34.7908C171.284 44.0595 173.696 53.2013 177.378 61.8351C178.521 64.5015 179.79 67.0408 181.187 69.4532C181.822 70.5959 183.219 74.1511 184.488 74.532C186.52 75.0398 189.186 74.532 191.218 74.532C191.599 74.532 191.852 74.278 191.979 74.0241C191.979 73.8971 191.979 73.8971 192.106 73.7701C193.376 58.407 194.392 43.0438 194.9 27.5537C195.154 19.9355 195.661 11.8096 194.392 4.0645Z"
        fill="url(#paint7_linear)"
      />
      <path
        d="M183.092 75.039C189.06 76.1817 195.535 76.0548 201.503 75.039C203.28 74.7851 202.518 72.3727 200.741 72.7536C195.281 73.7693 189.314 73.7693 183.727 72.7536C182.076 72.3727 181.442 74.6581 183.092 75.039Z"
        fill={primary}
      />
      <text
        transform="translate(10 238.38)"
        x="50%"
        dominantBaseline="middle"
        textAnchor="middle"
        fill={dark}
        style={{ fontWeight: 600, textTransform: 'uppercase', fontSize: 22, letterSpacing: 2 }}
      >
        {text}
      </text>
      <path
        d="M234.152 191.779H217.266V213.998H208.505V191.779H160.638V185.431L205.585 129.311H215.234L171.938 184.034H208.759V164.481H217.266V184.034H234.152V191.779Z"
        fill={dark}
      />
      <path
        d="M258.404 209.555C253.325 206.127 249.262 201.048 246.469 194.572C243.549 188.097 242.152 180.479 242.152 171.591C242.152 162.83 243.549 155.085 246.469 148.61C249.389 142.134 253.325 137.183 258.404 133.628C263.483 130.199 269.323 128.422 275.925 128.422C282.401 128.422 288.241 130.199 293.447 133.628C298.526 137.056 302.589 142.134 305.509 148.61C308.429 155.085 309.826 162.703 309.826 171.591C309.826 180.352 308.429 188.097 305.509 194.572C302.589 201.048 298.526 206 293.447 209.555C288.368 212.983 282.528 214.76 275.925 214.76C269.45 214.76 263.61 213.11 258.404 209.555ZM289.003 202.698C292.812 199.905 295.733 195.969 297.764 190.763C299.922 185.558 300.938 179.209 300.938 171.718C300.938 164.354 299.922 158.006 297.764 152.673C295.606 147.467 292.685 143.404 289.003 140.738C285.194 137.945 280.877 136.675 276.052 136.675C271.101 136.675 266.784 138.071 263.102 140.738C259.293 143.531 256.372 147.467 254.341 152.673C252.182 157.879 251.167 164.227 251.167 171.718C251.167 179.082 252.182 185.431 254.341 190.763C256.499 195.969 259.42 200.032 263.102 202.698C266.911 205.492 271.228 206.761 276.052 206.761C280.877 206.761 285.194 205.492 289.003 202.698Z"
        fill={dark}
      />
      <path
        d="M394.387 191.779H377.5V213.998H368.739V191.779H320.872V185.431L365.819 129.311H375.468L332.172 184.034H368.993V164.481H377.5V184.034H394.387V191.779Z"
        fill={dark}
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear"
        x1="362.93"
        y1="369.602"
        x2="432.262"
        y2="369.602"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint1_linear"
        x1="270.712"
        y1="370.982"
        x2="334.438"
        y2="370.982"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint2_linear"
        x1="338.802"
        y1="360.755"
        x2="401.477"
        y2="360.755"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint3_linear"
        x1="296.763"
        y1="360.464"
        x2="357.026"
        y2="360.464"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint4_linear"
        x1="197.699"
        y1="41.2278"
        x2="227.764"
        y2="41.2278"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint5_linear"
        x1="157.754"
        y1="41.9118"
        x2="185.377"
        y2="41.9118"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint6_linear"
        x1="187.422"
        y1="37.4543"
        x2="214.579"
        y2="37.4543"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <linearGradient
        id="paint7_linear"
        x1="169.105"
        y1="37.2542"
        x2="195.217"
        y2="37.2542"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor={secondLight} />
        <stop offset=".82" stopColor={secondDark} />
        <stop offset="1" stopColor={darkest} />
      </linearGradient>
      <clipPath id="clip0">
        <rect width="540" height="517.5" fill="white" />
      </clipPath>
    </defs>
  </svg>
)
