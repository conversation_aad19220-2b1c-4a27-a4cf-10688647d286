import React from 'react'

export const marker = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M16.5264 5.06211C19.1917 7.81864 19.1917 12.2636 16.5264 15.0201L11.7002 20.0001L6.87393 15.0211C4.20869 12.2646 4.20869 7.81964 6.87393 5.06311C8.15004 3.74291 9.88743 3.00019 11.7 3C13.5125 2.99981 15.2501 3.74218 16.5264 5.06211Z"
      stroke={stroke || dark}
      strokeWidth={strokeWidth || 1.4}
      fillRule="evenodd"
      clipRule="evenodd"
      strokeLinecap="round"
      strokeLinejoin="round"
    ></path>
    <path
      d="M14.4301 9.61108C14.4124 11.153 13.1824 12.3901 11.679 12.378C10.1755 12.3659 8.96452 11.1093 8.97036 9.56721C8.9762 8.02514 10.1966 6.77818 11.7001 6.77808C13.2167 6.78744 14.4388 8.05567 14.4301 9.61108Z"
      stroke={stroke || dark}
      strokeWidth={strokeWidth || 1.4}
      fillRule="evenodd"
      clipRule="evenodd"
      strokeLinecap="round"
      strokeLinejoin="round"
    ></path>
  </svg>
)
