import React from 'react'

export const stateDeleted = ({
  fill,
  width,
  height,
  stroke,
  strokeWidth,
  theme: {
    color: {
      general: { gray6 },
      status: { error },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M8.99976 18H5.49975C4.39518 17.9999 3.49975 17.1045 3.49976 15.9999L3.49983 3.99999C3.49984 2.89542 4.39527 2 5.49983 2H14.5001C15.6046 2 16.5001 2.89543 16.5001 4V10.5M7.0835 6H13.0835M7.0835 9H13.0835M7.0835 12H10.0835"
      stroke={stroke || gray6}
      strokeWidth={strokeWidth || 1.4}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 18L14.5 16M14.5 16L12.5 14M14.5 16L12.5 18M14.5 16L16.5 14"
      stroke={error}
      strokeWidth={strokeWidth || 1.4}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
