import React from 'react'

export const home = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M17.45 9L16 7.551V4C16 3.45 15.55 3 15 3H14C13.45 3 13 3.45 13 4V4.553L11 2.555C10.727 2.297 10.477 2 10 2C9.523 2 9.273 2.297 9 2.555L2.55 9C2.238 9.325 2 9.562 2 10C2 10.563 2.432 11 3 11H4V17C4 17.55 4.45 18 5 18H7C7.55228 18 8 17.5523 8 17V13C8 12.45 8.45 12 9 12H11C11.55 12 12 12.45 12 13V17C12 17.5523 11.9477 18 12.5 18H15C15.55 18 16 17.55 16 17V11H17C17.568 11 18 10.563 18 10C18 9.562 17.762 9.325 17.45 9Z"
      stroke={stroke || dark}
      strokeWidth={strokeWidth || 1.4}
      strokeLinejoin="round"
    />
  </svg>
)
