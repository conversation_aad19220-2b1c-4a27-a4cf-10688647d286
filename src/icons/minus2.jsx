import React from 'react'

export const minus2 = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray5 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M16 10L4 10"
      stroke={stroke || fill || gray5}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
    />
  </svg>
)
