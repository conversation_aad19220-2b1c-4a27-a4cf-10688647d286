import React from 'react'

export const tShirt = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray6 },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M3.63641 8.09094H5.2455C5.41118 8.09094 5.5455 8.22525 5.5455 8.39094V16.6818C5.5455 16.8575 5.68804 17 5.86368 17H14.1364C14.312 17 14.4546 16.8575 14.4546 16.6818V8.39094C14.4546 8.22525 14.5889 8.09094 14.7546 8.09094H16.3636C16.5259 8.09094 16.6618 7.96908 16.6799 7.80776L16.9981 4.94413C17.0146 4.7949 16.9249 4.65459 16.7827 4.60718L12.01 3.01627C11.8875 2.97555 11.7519 3.01277 11.6676 3.11109C10.8403 4.1245 9.16003 4.12386 8.33276 3.11109C8.24844 3.01309 8.11321 2.97555 7.9904 3.01627L3.21768 4.60718C3.07514 4.65459 2.98541 4.79522 3.00196 4.94413L3.32014 7.80776C3.33828 7.96908 3.47414 8.09094 3.63641 8.09094Z"
      stroke={fill || stroke || gray6}
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
