import React from 'react'

export const calendarAdd = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 21} height={height || 20} viewBox="0 0 21 20" fill="none">
    <path
      d="M3.85413 7.05762H16.9791M5.55055 1.66699V3.07341M15.1041 1.66699V3.07324M15.1041 3.07324H5.72913C4.17583 3.07324 2.91663 4.33244 2.91663 5.88574V15.2608C2.91663 16.8141 4.17583 18.0733 5.72913 18.0733H15.1041C16.6574 18.0733 17.9166 16.8141 17.9166 15.2608L17.9166 5.88574C17.9166 4.33244 16.6574 3.07324 15.1041 3.07324ZM10.4166 9.87016V12.2139M10.4166 12.2139V14.5577M10.4166 12.2139H12.7604M10.4166 12.2139H8.07288"
      stroke={stroke || fill || dark}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={strokeWidth || 2}
    />
  </svg>
)
