import React from 'react'

export const fileCheckList = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M10.4006 6.20039H14.6006M10.4006 9.90049H14.6006M10.4006 13.6005H14.6006M5.40026 6.04335L6.02883 6.67192L7.40026 5.30043M5.40026 10.0433L6.02883 10.6719L7.40026 9.30043M5.40026 14.0433L6.02883 14.6719L7.40026 13.3004M4.60029 1.40039H15.4005C16.726 1.40039 17.8005 2.47493 17.8005 3.80044L17.8002 16.2004C17.8002 17.5259 16.7257 18.6004 15.4002 18.6004L4.60019 18.6003C3.2747 18.6003 2.20019 17.5258 2.2002 16.2003L2.20029 3.80038C2.2003 2.4749 3.27481 1.40039 4.60029 1.40039Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
