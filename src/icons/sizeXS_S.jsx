import React from 'react'

export const sizeXS_S = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 18} height={height || 6} viewBox="0 0 18 6">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.5559 5.97826C15.1005 5.97826 14.6589 5.91791 14.2309 5.7972C13.8085 5.67102 13.4683 5.50917 13.2104 5.31165L13.6631 4.30764C13.91 4.48869 14.2035 4.63408 14.5437 4.74381C14.8838 4.85354 15.224 4.9084 15.5641 4.9084C15.9427 4.9084 16.2225 4.85354 16.4036 4.74381C16.5846 4.62859 16.6751 4.47772 16.6751 4.29118C16.6751 4.15402 16.6203 4.04155 16.5105 3.95376C16.4063 3.86049 16.2691 3.78643 16.0991 3.73156C15.9345 3.6767 15.7095 3.61635 15.4242 3.55051C14.9853 3.44627 14.6259 3.34202 14.3461 3.23778C14.0663 3.13354 13.8249 2.9662 13.6219 2.73577C13.4244 2.50534 13.3257 2.1981 13.3257 1.81405C13.3257 1.47938 13.4162 1.17763 13.5972 0.908791C13.7783 0.634469 14.0499 0.417755 14.412 0.258649C14.7796 0.0995423 15.2267 0.019989 15.7534 0.019989C16.121 0.019989 16.4804 0.0638804 16.8315 0.151663C17.1826 0.239446 17.4899 0.365634 17.7532 0.530227L17.3417 1.54247C16.8095 1.24072 16.2774 1.08984 15.7452 1.08984C15.3721 1.08984 15.095 1.15019 14.914 1.2709C14.7384 1.3916 14.6506 1.5507 14.6506 1.74822C14.6506 1.94573 14.7521 2.09386 14.9551 2.19262C15.1636 2.28589 15.4791 2.37915 15.9015 2.47242C16.3405 2.57667 16.6998 2.68091 16.9796 2.78515C17.2594 2.88939 17.4981 3.05399 17.6956 3.27893C17.8986 3.50387 18.0001 3.80837 18.0001 4.19242C18.0001 4.52161 17.9068 4.82336 17.7203 5.09768C17.5392 5.36652 17.2649 5.58049 16.8973 5.7396C16.5297 5.8987 16.0826 5.97826 15.5559 5.97826ZM4.43465 5.97827L2.99616 3.9099L1.5832 5.97827H0L2.20456 2.94806L0.110653 0.0200012H1.67683L3.04723 1.95218L4.39209 0.0200012H5.88166L3.80478 2.89699L6.02636 5.97827H4.43465ZM12.7846 2.57352H11.0823V3.4247H12.7846V2.57352ZM6.74039 5.79722C7.16833 5.91792 7.60999 5.97827 8.06536 5.97827C8.59206 5.97827 9.0392 5.89871 9.40679 5.73961C9.77439 5.5805 10.0487 5.36653 10.2298 5.0977C10.4163 4.82337 10.5096 4.52162 10.5096 4.19243C10.5096 3.80838 10.4081 3.50389 10.2051 3.27894C10.0076 3.054 9.7689 2.88941 9.48909 2.78516C9.20928 2.68092 8.84992 2.57668 8.41101 2.47244C7.98855 2.37917 7.67308 2.2859 7.4646 2.19263C7.2616 2.09387 7.1601 1.94574 7.1601 1.74823C7.1601 1.55072 7.24788 1.39161 7.42345 1.27091C7.6045 1.15021 7.88157 1.08986 8.25464 1.08986C8.78683 1.08986 9.31901 1.24073 9.85119 1.54249L10.2627 0.530239C9.99933 0.365646 9.69209 0.239459 9.34096 0.151675C8.98983 0.0638926 8.63046 0.0200012 8.26287 0.0200012C7.73618 0.0200012 7.28903 0.0995545 6.92144 0.258661C6.55933 0.417767 6.28776 0.634481 6.1067 0.908803C5.92565 1.17764 5.83513 1.47939 5.83513 1.81406C5.83513 2.19811 5.93388 2.50536 6.13139 2.73579C6.33439 2.96622 6.57579 3.13355 6.8556 3.23779C7.13541 3.34204 7.49477 3.44628 7.93369 3.55052C8.21898 3.61636 8.44392 3.67671 8.60852 3.73157C8.7786 3.78644 8.91576 3.8605 9.02 3.95377C9.12973 4.04156 9.18459 4.15403 9.18459 4.29119C9.18459 4.47773 9.09407 4.62861 8.91302 4.74382C8.73196 4.85355 8.45215 4.90841 8.07359 4.90841C7.73343 4.90841 7.39327 4.85355 7.05311 4.74382C6.71296 4.63409 6.41943 4.4887 6.17254 4.30765L5.71991 5.31167C5.97777 5.50918 6.31793 5.67103 6.74039 5.79722Z"
      fill={fill || dark}
    />
  </svg>
)
