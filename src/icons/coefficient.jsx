import React from 'react'

export const coefficient = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M8.00008 6.50001C8.00008 6.28537 7.86307 6.09468 7.65964 6.02618C7.45622 5.95768 7.23176 6.02666 7.10192 6.19758C7.02688 6.29636 6.95548 6.39856 6.88196 6.5038C6.55222 6.97581 6.17964 7.50913 5.24283 8.07119C5.00604 8.21326 4.92926 8.52037 5.07133 8.75715C5.2134 8.99393 5.52053 9.07071 5.75732 8.92864C6.30164 8.60207 6.69803 8.26855 7.00008 7.95668V13.5C7.00008 13.7763 7.22412 14.0002 7.50039 14C7.77641 13.9998 8.00008 13.776 8.00008 13.5V6.50001ZM10.8536 10.1463C10.6583 9.95106 10.3417 9.95106 10.1464 10.1463C9.95118 10.3416 9.95118 10.6581 10.1464 10.8534L11.2929 11.9998L10.1464 13.1462C9.95118 13.3414 9.95118 13.658 10.1464 13.8533C10.3417 14.0485 10.6583 14.0485 10.8536 13.8533L12 12.7069L13.1464 13.8533C13.3417 14.0485 13.6583 14.0485 13.8536 13.8533C14.0488 13.658 14.0488 13.3414 13.8536 13.1462L12.7071 11.9998L13.8536 10.8534C14.0488 10.6581 14.0488 10.3416 13.8536 10.1463C13.6583 9.95106 13.3417 9.95106 13.1464 10.1463L12 11.2927L10.8536 10.1463Z"
      fill={fill || dark}
    />
  </svg>
)
