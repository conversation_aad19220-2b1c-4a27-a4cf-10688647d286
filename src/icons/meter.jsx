import React from 'react'

export const meter = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32">
    <path d="M29.12,24.92l-22-22a2.92,2.92,0,0,0-3.24-.65A2.92,2.92,0,0,0,2,5V29a1,1,0,0,0,1,1H27a3,3,0,0,0,2.1-5.08Zm-1.2,2.48a1,1,0,0,1-.9.6H24V27a1,1,0,0,0-2,0v1H20V27a1,1,0,0,0-2,0v1H16V27a1,1,0,0,0-2,0v1H12V27a1,1,0,0,0-2,0v1H8V27a1,1,0,0,0-2,0v1H4V26H5a1,1,0,0,0,0-2H4V22H5a1,1,0,0,0,0-2H4V18H5a1,1,0,0,0,0-2H4V14H5a1,1,0,0,0,0-2H4V10H5A1,1,0,0,0,5,8H4V5a1,1,0,0,1,1.66-.69L27.71,26.34A.94.94,0,0,1,27.92,27.4Z" />
    <path d="M9.71,14a1,1,0,0,0-1.09-.22A1,1,0,0,0,8,14.7V23a1,1,0,0,0,1,1h8.3A1,1,0,0,0,18,22.29ZM10,22V17.11L14.89,22Z" />
  </svg>
)
