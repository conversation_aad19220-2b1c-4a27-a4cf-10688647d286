import React from 'react'

export const linkExternal = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M9 2H5C3.34315 2 2 3.34315 2 5V15.0001C2 16.6569 3.34315 18.0001 5 18.0001H15C16.6569 18.0001 18 16.6569 18 15.0001V11M12.9995 2.00024L18 2M18 2V6.50012M18 2L9.49927 10.4998"
      stroke={stroke || fill || dark}
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
