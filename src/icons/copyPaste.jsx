import React from 'react'

export const copyPaste = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { gray6, dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M5 12H3C2.44771 12 2 11.5523 2 11L2 4C2 2.89543 2.89543 2 4 2L11 2C11.5523 2 12 2.44772 12 3V5M10.5 13H15.5M10.5 15.5H15.5M10.5 10.5H15.5M10 18L16 18C17.1046 18 18 17.1046 18 16L18 10C18 8.89543 17.1046 8 16 8L10 8C8.89543 8 8 8.89543 8 10L8 16C8 17.1046 8.89543 18 10 18Z"
      fill={fill || stroke || dark}
      stroke={stroke || gray6}
      strokeWidth={strokeWidth || 2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
