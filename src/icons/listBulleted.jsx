import React from 'react'

export const listBulleted = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M7.26667 5H18M7.26667 10.4H18M7.26667 15.8H18M3 5V5.01067M3 10.4V10.4107M3 15.8V15.8107"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
