import React from 'react'

export const filter2 = ({ width = 18, height = 18, fill }) => (
  <svg width={width} height={height} viewBox="0 0 18 18" fill="none">
    <mask id="path-1-inside" fill={fill || 'white'}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.8293 2H17C17.5523 2 18 2.44772 18 3C18 3.55228 17.5523 4 17 4H16.8293C16.4175 5.16519 15.3062 6 14 6C12.6938 6 11.5825 5.16519 11.1707 4H1C0.447715 4 0 3.55228 0 3C0 2.44772 0.447715 2 1 2H11.1707C11.5825 0.834808 12.6938 0 14 0C15.3062 0 16.4175 0.834808 16.8293 2ZM1 8C0.447715 8 0 8.44772 0 9C0 9.55229 0.447715 10 1 10H6.17071C6.58254 11.1652 7.69378 12 9 12C10.3062 12 11.4175 11.1652 11.8293 10H17C17.5523 10 18 9.55229 18 9C18 8.44771 17.5523 8 17 8H11.8293C11.4175 6.83481 10.3062 6 9 6C7.69378 6 6.58254 6.83481 6.17071 8H1ZM0 15C0 14.4477 0.447715 14 1 14H11.1707C11.5825 12.8348 12.6938 12 14 12C15.3062 12 16.4175 12.8348 16.8293 14H17C17.5523 14 18 14.4477 18 15C18 15.5523 17.5523 16 17 16H16.8293C16.4175 17.1652 15.3062 18 14 18C12.6938 18 11.5825 17.1652 11.1707 16H1C0.447715 16 0 15.5523 0 15Z"
      />
    </mask>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.8293 2H17C17.5523 2 18 2.44772 18 3C18 3.55228 17.5523 4 17 4H16.8293C16.4175 5.16519 15.3062 6 14 6C12.6938 6 11.5825 5.16519 11.1707 4H1C0.447715 4 0 3.55228 0 3C0 2.44772 0.447715 2 1 2H11.1707C11.5825 0.834808 12.6938 0 14 0C15.3062 0 16.4175 0.834808 16.8293 2ZM1 8C0.447715 8 0 8.44772 0 9C0 9.55229 0.447715 10 1 10H6.17071C6.58254 11.1652 7.69378 12 9 12C10.3062 12 11.4175 11.1652 11.8293 10H17C17.5523 10 18 9.55229 18 9C18 8.44771 17.5523 8 17 8H11.8293C11.4175 6.83481 10.3062 6 9 6C7.69378 6 6.58254 6.83481 6.17071 8H1ZM0 15C0 14.4477 0.447715 14 1 14H11.1707C11.5825 12.8348 12.6938 12 14 12C15.3062 12 16.4175 12.8348 16.8293 14H17C17.5523 14 18 14.4477 18 15C18 15.5523 17.5523 16 17 16H16.8293C16.4175 17.1652 15.3062 18 14 18C12.6938 18 11.5825 17.1652 11.1707 16H1C0.447715 16 0 15.5523 0 15Z"
      // fill="#F9A600"
    />
    <path
      d="M16.8293 2L14.9436 2.66649L15.4149 4H16.8293V2ZM16.8293 4V2H15.4149L14.9436 3.33351L16.8293 4ZM11.1707 4L13.0564 3.33351L12.5851 2H11.1707V4ZM11.1707 2V4H12.5851L13.0564 2.66649L11.1707 2ZM6.17071 10L8.05639 9.33351L7.58506 8H6.17071V10ZM11.8293 10V8H10.4149L9.94361 9.33351L11.8293 10ZM11.8293 8L9.94361 8.66649L10.4149 10H11.8293V8ZM6.17071 8V10H7.58506L8.05639 8.66649L6.17071 8ZM11.1707 14V16H12.5851L13.0564 14.6665L11.1707 14ZM16.8293 14L14.9436 14.6665L15.4149 16H16.8293V14ZM16.8293 16V14H15.4149L14.9436 15.3335L16.8293 16ZM11.1707 16L13.0564 15.3335L12.5851 14H11.1707V16ZM16.8293 4H17V0H16.8293V4ZM17 4C16.4477 4 16 3.55229 16 3H20C20 1.34315 18.6569 0 17 0V4ZM16 3C16 2.44771 16.4477 2 17 2V6C18.6569 6 20 4.65685 20 3H16ZM17 2H16.8293V6H17V2ZM14.9436 3.33351C14.8054 3.7246 14.4323 4 14 4V8C16.1802 8 18.0295 6.60578 18.715 4.66649L14.9436 3.33351ZM14 4C13.5677 4 13.1946 3.7246 13.0564 3.33351L9.28503 4.66649C9.97047 6.60578 11.8198 8 14 8V4ZM11.1707 2H1V6H11.1707V2ZM1 2C1.55228 2 2 2.44772 2 3H-2C-2 4.65685 -0.656854 6 1 6V2ZM2 3C2 3.55228 1.55228 4 1 4V0C-0.656854 0 -2 1.34315 -2 3H2ZM1 4H11.1707V0H1V4ZM13.0564 2.66649C13.1946 2.2754 13.5677 2 14 2V-2C11.8198 -2 9.97047 -0.605782 9.28503 1.33351L13.0564 2.66649ZM14 2C14.4323 2 14.8054 2.2754 14.9436 2.66649L18.715 1.33351C18.0295 -0.605782 16.1802 -2 14 -2V2ZM2 9C2 9.55228 1.55229 10 1 10V6C-0.656855 6 -2 7.34315 -2 9H2ZM1 8C1.55228 8 2 8.44771 2 9H-2C-2 10.6569 -0.656854 12 1 12V8ZM6.17071 8H1V12H6.17071V8ZM9 10C8.56772 10 8.19462 9.7246 8.05639 9.33351L4.28503 10.6665C4.97047 12.6058 6.81984 14 9 14V10ZM9.94361 9.33351C9.80538 9.7246 9.43228 10 9 10V14C11.1802 14 13.0295 12.6058 13.715 10.6665L9.94361 9.33351ZM17 8H11.8293V12H17V8ZM16 9C16 8.44771 16.4477 8 17 8V12C18.6569 12 20 10.6569 20 9H16ZM17 10C16.4477 10 16 9.55229 16 9H20C20 7.34314 18.6569 6 17 6V10ZM11.8293 10H17V6H11.8293V10ZM9 8C9.43228 8 9.80538 8.2754 9.94361 8.66649L13.715 7.33351C13.0295 5.39422 11.1802 4 9 4V8ZM8.05639 8.66649C8.19462 8.2754 8.56772 8 9 8V4C6.81984 4 4.97047 5.39422 4.28503 7.33351L8.05639 8.66649ZM1 10H6.17071V6H1V10ZM1 12C-0.656855 12 -2 13.3431 -2 15H2C2 15.5523 1.55229 16 1 16V12ZM11.1707 12H1V16H11.1707V12ZM14 10C11.8198 10 9.97047 11.3942 9.28503 13.3335L13.0564 14.6665C13.1946 14.2754 13.5677 14 14 14V10ZM18.715 13.3335C18.0295 11.3942 16.1802 10 14 10V14C14.4323 14 14.8054 14.2754 14.9436 14.6665L18.715 13.3335ZM17 12H16.8293V16H17V12ZM20 15C20 13.3431 18.6569 12 17 12V16C16.4477 16 16 15.5523 16 15H20ZM17 18C18.6569 18 20 16.6569 20 15H16C16 14.4477 16.4477 14 17 14V18ZM16.8293 18H17V14H16.8293V18ZM14 20C16.1802 20 18.0295 18.6058 18.715 16.6665L14.9436 15.3335C14.8054 15.7246 14.4323 16 14 16V20ZM9.28503 16.6665C9.97047 18.6058 11.8198 20 14 20V16C13.5677 16 13.1946 15.7246 13.0564 15.3335L9.28503 16.6665ZM1 18H11.1707V14H1V18ZM-2 15C-2 16.6569 -0.656854 18 1 18V14C1.55228 14 2 14.4477 2 15H-2Z"
      fill={fill || 'white'}
      mask="url(#path-1-inside)"
    />
  </svg>
)
