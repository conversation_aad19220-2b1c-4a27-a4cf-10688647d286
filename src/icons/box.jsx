import React from 'react'

export const box = ({
  fill,
  width,
  height,
  strokeWidth,
  stroke,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M17.9435 6H2.05653M12.4997 8.74998C10.5471 8.74998 7.49967 8.74998 7.49967 8.74998M18 6.47214V16C18 17.1046 17.1046 18 16 18H4C2.89543 18 2 17.1046 2 16V6.47214C2 6.16165 2.07229 5.85542 2.21115 5.57771L3.58541 2.82918C3.8395 2.321 4.35889 2 4.92705 2H15.0729C15.6411 2 16.1605 2.321 16.4146 2.82918L17.7889 5.57771C17.9277 5.85542 18 6.16165 18 6.47214Z"
      stroke={stroke || dark}
      strokeWidth={strokeWidth || 1.4}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
