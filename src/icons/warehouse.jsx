import React from 'react'

export const warehouse = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M14.5 8.2L10 5.7L5.5 8.2M14.5 8.2V13.2L10 15.7M14.5 8.2L12.25 9.60625M10 15.7L5.5 13.2V8.2M10 15.7V11.0125M5.5 8.2L10 11.0125M10 11.0125L12.25 9.60625M12.25 9.60625L7.75 6.95M9.38184 2.18771L2.44851 6.87647C2.16713 7.06676 2 7.3748 2 7.70316V16.4784C2 17.3188 2.71634 18 3.6 18H16.4C17.2837 18 18 17.3188 18 16.4784V7.70316C18 7.3748 17.8329 7.06676 17.5515 6.87647L10.6182 2.18771C10.2481 1.93743 9.75193 1.93743 9.38184 2.18771Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
