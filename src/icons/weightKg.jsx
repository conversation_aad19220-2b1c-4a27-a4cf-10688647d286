import React from 'react'

export const weightKg = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || '16'} height={height || '17'} viewBox="0 0 16 17" fill="none">
    <g clipPath="url(#clip0_8386_85880)">
      <path
        d="M15.5328 13.0941L14.3475 6.38519C14.1022 4.99659 12.9006 3.98875 11.4906 3.98875H9.81376C10.0673 3.62838 10.2168 3.18978 10.2168 2.71666C10.2168 1.49441 9.22233 0.5 8.00008 0.5C6.77783 0.5 5.78339 1.49441 5.78339 2.71669C5.78339 3.18981 5.93286 3.62841 6.18639 3.98878H4.50958C3.09951 3.98878 1.89798 4.99662 1.65264 6.38522L0.467389 13.0941C0.317795 13.9409 0.548983 14.8044 1.10161 15.4633C1.65426 16.1222 2.46439 16.5 3.32436 16.5H12.6759C13.5358 16.5 14.346 16.1221 14.8986 15.4633C15.4512 14.8045 15.6823 13.9409 15.5328 13.0941ZM8.00008 1.44459C8.70152 1.44459 9.27217 2.01525 9.27217 2.71669C9.27217 3.41812 8.70148 3.98878 8.00008 3.98878C7.29867 3.98878 6.72798 3.41812 6.72798 2.71669C6.72798 2.01525 7.29864 1.44459 8.00008 1.44459ZM14.1749 14.8562C13.8021 15.3006 13.2558 15.5554 12.6759 15.5554H3.32436C2.74442 15.5554 2.19805 15.3006 1.82536 14.8562C1.45267 14.4119 1.29673 13.8295 1.39764 13.2584L2.58289 6.54956C2.74836 5.61309 3.55867 4.93334 4.50961 4.93334H11.4906C12.4416 4.93334 13.2519 5.61303 13.4174 6.5495L14.6026 13.2584C14.7035 13.8295 14.5475 14.4119 14.1749 14.8562Z"
        fill={fill || '#0065FF'}
      />
      <path
        d="M6.44712 10.2443L7.63066 9.02133C7.81206 8.83392 7.80712 8.53492 7.61969 8.35352C7.43222 8.17205 7.13322 8.17705 6.95181 8.36446L5.62822 9.73217V8.69289C5.62822 8.43205 5.41675 8.22058 5.15591 8.22058C4.89506 8.22058 4.68359 8.43205 4.68359 8.69289V11.7957C4.68359 12.0566 4.89506 12.268 5.15591 12.268C5.41675 12.268 5.62822 12.0566 5.62822 11.7957V10.7565L6.95181 12.1242C7.04444 12.2199 7.16778 12.268 7.29128 12.268C7.40953 12.268 7.52797 12.2239 7.61969 12.1351C7.80712 11.9537 7.812 11.6548 7.63066 11.4673L6.44712 10.2443Z"
        fill={fill || '#0065FF'}
      />
      <path
        d="M10.8444 9.90558H10.1354C9.87457 9.90558 9.66311 10.117 9.66311 10.3779C9.66311 10.6387 9.87457 10.8502 10.1354 10.8502H10.3643C10.3193 11.1185 10.0855 11.3236 9.80457 11.3236C9.49164 11.3236 9.23704 11.069 9.23704 10.756V9.73286C9.23704 9.41992 9.49164 9.16533 9.80457 9.16533C9.98064 9.16533 10.1439 9.24508 10.2524 9.3842C10.4129 9.58976 10.7097 9.62639 10.9154 9.46589C11.1209 9.30536 11.1575 9.00858 10.9971 8.80298C10.7083 8.43298 10.2736 8.22076 9.80461 8.22076C8.97082 8.22076 8.29248 8.89911 8.29248 9.73289V10.7561C8.29248 11.5899 8.97082 12.2682 9.80461 12.2682C10.6384 12.2682 11.3167 11.5899 11.3167 10.7561V10.378C11.3167 10.117 11.1053 9.90558 10.8444 9.90558Z"
        fill={fill || '#0065FF'}
      />
    </g>
    <defs>
      <clipPath id="clip0_8386_85880">
        <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
      </clipPath>
    </defs>
  </svg>
)
