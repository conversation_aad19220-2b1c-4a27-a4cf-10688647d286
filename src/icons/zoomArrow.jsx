import React from 'react'

export const zoomArrow = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M10.8421 9.15847L15.8926 4.10744M15.8926 4.10744L12.5254 4.10742M15.8926 4.10744L15.8925 7.47453M9.1582 10.8418L4.10771 15.8928M4.10771 15.8928L7.47487 15.8928M4.10771 15.8928L4.10776 12.5257M10.8415 10.8421L15.8926 15.8926M15.8926 15.8926L15.8926 12.5254M15.8926 15.8926L12.5255 15.8925M9.15847 9.15793L4.10744 4.10744M4.10744 4.10744L4.10742 7.4746M4.10744 4.10744L7.47453 4.10748"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
