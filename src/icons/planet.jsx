import React from 'react'

export const planet = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M16 15.2916L13.0769 11.8462H11.2308L10 10.6154L12.4615 6.92308H16.7692M5.69231 3.84615L6.99765 5.1515C7.69907 5.85291 7.91498 6.90485 7.54658 7.82585V7.82585C7.17001 8.76729 6.2582 9.38461 5.24424 9.38461H2.61538M18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10Z"
      stroke={stroke || fill || dark}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
