import React from 'react'

export const settingsCard = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M9.12709 1.66667C8.79979 1.66667 8.49321 1.82684 8.30627 2.09549L7.29677 3.54622C7.00772 3.9586 6.52727 4.16066 6.01731 4.12464L4.22823 4.01232C3.90738 3.99217 3.59641 4.12751 3.39249 4.37604L2.55403 5.39792C2.33783 5.66142 2.27073 6.01689 2.37599 6.34107L2.89437 7.93754C2.96848 8.17782 2.97796 8.43523 2.92493 8.67834C2.87119 8.91858 2.756 9.14542 2.58496 9.3291L1.40749 10.5955C1.17909 10.8411 1.08906 11.1849 1.16775 11.511L1.46129 12.7274C1.53972 13.0524 1.77536 13.3167 2.08927 13.4318L3.70707 14.0247C3.94448 14.1166 4.14959 14.2659 4.30482 14.4541C4.459 14.6427 4.56401 14.8704 4.60475 15.117L4.88028 16.8094C4.93355 17.1366 5.1455 17.4161 5.44618 17.5557L6.64542 18.1125C6.93649 18.2477 7.27473 18.2349 7.55479 18.0782L9.07528 17.2276C9.29619 17.1035 9.54274 17.0413 9.78929 17.041C9.91748 17.0412 10.0457 17.0582 10.1707 17.0917C10.287 17.1228 10.4 17.1682 10.5072 17.2276L12.1021 18.091C12.3834 18.2433 12.7205 18.2518 13.0092 18.1139L14.2028 17.5438C14.5025 17.4007 14.7115 17.1182 14.7608 16.7899L15.0072 15.1483C15.0462 14.9004 15.1498 14.671 15.3036 14.4807C15.4564 14.2923 15.659 14.1422 15.895 14.0495L17.5322 13.4078C17.8401 13.2871 18.0685 13.022 18.1422 12.6995L18.4244 11.4639C18.4978 11.1423 18.4079 10.8052 18.1841 10.5628L16.9978 9.27831C16.8236 9.09398 16.7021 8.87379 16.6431 8.63978C16.5852 8.40745 16.5887 8.16124 16.6621 7.92294L17.1741 6.28167C17.2745 5.95987 17.206 5.60915 16.9919 5.34876L16.1769 4.35747C15.9706 4.10658 15.6554 3.9715 15.3314 3.99523L13.5648 4.12464C13.3112 4.14254 13.0618 4.09424 12.8399 3.99033C12.6165 3.88509 12.4205 3.72325 12.2748 3.51694L11.2764 2.09267C11.0892 1.82566 10.7836 1.66667 10.4575 1.66667H9.12709ZM9.8142 13.128C11.4031 13.128 12.6913 11.8681 12.6913 10.314C12.6913 8.75988 11.4031 7.50005 9.8142 7.50005C8.22534 7.50005 6.93711 8.75988 6.93711 10.314C6.93711 11.8681 8.22534 13.128 9.8142 13.128Z"
      fill={fill || stroke || dark}
      fillRule="evenodd"
      clipRule="evenodd"
    />
    <path
      d="M7.29677 3.54622L8.30627 2.09549C8.49321 1.82684 8.79979 1.66667 9.12709 1.66667H10.4575C10.7836 1.66667 11.0892 1.82566 11.2764 2.09267L12.2748 3.51694M7.29677 3.54622C7.00772 3.9586 6.52727 4.16066 6.01731 4.12464M7.29677 3.54622C7.00632 3.95761 6.52727 4.16234 6.01731 4.12464M6.01731 4.12464L4.22823 4.01232C3.90738 3.99217 3.59641 4.12751 3.39249 4.37604L2.55403 5.39792C2.33783 5.66142 2.27073 6.01689 2.37599 6.34107L2.89437 7.93754M2.89437 7.93754C2.96848 8.17782 2.97796 8.43523 2.92493 8.67834M2.89437 7.93754C2.96953 8.17739 2.97866 8.43428 2.92457 8.68083M2.92493 8.67834C2.87119 8.91858 2.756 9.14542 2.58496 9.3291M2.92493 8.67834L2.92457 8.68083M2.58496 9.3291L1.40749 10.5955C1.17909 10.8411 1.08906 11.1849 1.16775 11.511L1.46129 12.7274C1.53972 13.0524 1.77536 13.3167 2.08927 13.4318L3.70707 14.0247C3.94448 14.1166 4.14959 14.2659 4.30482 14.4541M2.58496 9.3291C2.75705 9.14692 2.87119 8.92081 2.92457 8.68083M4.30482 14.4541C4.459 14.6427 4.56401 14.8704 4.60475 15.117M4.30482 14.4541C4.46041 14.6427 4.56577 14.8698 4.60475 15.117M4.30482 14.4541L4.30236 14.4513C4.29218 14.439 4.28129 14.4261 4.27076 14.4142M4.60475 15.117L4.88028 16.8094C4.93355 17.1366 5.1455 17.4161 5.44618 17.5557L6.64542 18.1125C6.93649 18.2477 7.27473 18.2349 7.55479 18.0782L9.07528 17.2276M9.07528 17.2276C9.29619 17.1035 9.54274 17.0413 9.78929 17.041M9.07528 17.2276C9.16273 17.1778 9.2544 17.1381 9.34852 17.1083C9.49146 17.063 9.64038 17.0407 9.78929 17.041M9.78929 17.041C9.91748 17.0412 10.0457 17.0582 10.1707 17.0917C10.287 17.1228 10.4 17.1682 10.5072 17.2276M9.78929 17.041C10.0369 17.0406 10.2848 17.1028 10.5072 17.2276M10.5072 17.2276L12.1021 18.091C12.3834 18.2433 12.7205 18.2518 13.0092 18.1139L14.2028 17.5438C14.5025 17.4007 14.7115 17.1182 14.7608 16.7899L15.0072 15.1483M15.0072 15.1483C15.0462 14.9004 15.1498 14.671 15.3036 14.4807M15.0072 15.1483C15.0448 14.8998 15.1491 14.6708 15.3036 14.4807M15.3036 14.4807C15.4564 14.2923 15.659 14.1422 15.895 14.0495M15.3036 14.4807C15.456 14.2916 15.658 14.1411 15.895 14.0495M15.895 14.0495L17.5322 13.4078C17.8401 13.2871 18.0685 13.022 18.1422 12.6995L18.4244 11.4639C18.4978 11.1423 18.4079 10.8052 18.1841 10.5628L16.9978 9.27831M16.9978 9.27831C16.8236 9.09398 16.7021 8.87379 16.6431 8.63978M16.9978 9.27831C16.916 9.19222 16.8458 9.0981 16.7885 8.99831C16.7232 8.885 16.6744 8.76439 16.6431 8.63978M16.6431 8.63978C16.5852 8.40745 16.5887 8.16124 16.6621 7.92294L17.1741 6.28167C17.2745 5.95987 17.206 5.60915 16.9919 5.34876L16.1769 4.35747C15.9706 4.10658 15.6554 3.9715 15.3314 3.99523L13.5648 4.12464M13.5648 4.12464C13.3112 4.14254 13.0618 4.09424 12.8399 3.99033M13.5648 4.12464C13.3109 4.14357 13.0618 4.09488 12.8399 3.99033M12.8399 3.99033C12.6165 3.88509 12.4205 3.72325 12.2748 3.51694M12.8399 3.99033C12.6162 3.88547 12.4198 3.72407 12.2748 3.51694M2.92457 8.68083L2.92528 8.67718M12.6913 10.314C12.6913 11.8681 11.4031 13.128 9.8142 13.128C8.22534 13.128 6.93711 11.8681 6.93711 10.314C6.93711 8.75988 8.22534 7.50005 9.8142 7.50005C11.4031 7.50005 12.6913 8.75988 12.6913 10.314Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
