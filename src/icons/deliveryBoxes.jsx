import React from 'react'

export const deliveryBoxes = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M18 5.24636L10 1.00003L2 5.24636M18 5.24636V14.4155L10 19M18 5.24636L10 9.73539M10 19L2 14.4155V5.24636M10 19V9.73539M2 5.24636L10 9.73539M13.9881 7.49935L6.168 3.03402"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
