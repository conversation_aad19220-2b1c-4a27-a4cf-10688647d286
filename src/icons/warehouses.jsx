import React from 'react'

export const warehouses = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M5.58431 7.71639L10.001 10.2747L14.3843 7.73305M10.0015 14.808V10.2664M18 5L18 15.0001C18 16.6569 16.6569 18.0001 15 18.0001H5C3.34315 18.0001 2 16.6569 2 15.0001V5C2 3.34315 3.34315 2 5 2H15C16.6569 2 18 3.34315 18 5ZM8.96688 5.24155L6.3002 6.72488C5.7002 7.05821 5.2002 7.89988 5.2002 8.59154V11.4165C5.2002 12.1082 5.69187 12.9499 6.3002 13.2832L8.96688 14.7665C9.53355 15.0832 10.4669 15.0832 11.0419 14.7665L13.7085 13.2832C14.3085 12.9499 14.8085 12.1082 14.8085 11.4165V8.58322C14.8085 7.89155 14.3169 7.04988 13.7085 6.71654L11.0419 5.23321C10.4669 4.91655 9.53355 4.91655 8.96688 5.24155Z"
      stroke={stroke || fill || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
