import React from 'react'

export const chartDummy = ({
  width,
  height,
  active,
  id,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 190} height={height || 82} viewBox="0 0 190 82" fill={`url(#${id || 'paint0'})`}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.11 42.4815C8.98 36.5556 10.85 36.5556 13.655 35.0741C15.525 33.5926 17.395 30.6296 19.265 33.5926C21.135 36.5556 23.005 45.4444 24.875 46.9259C26.745 48.4074 28.615 42.4815 30.485 33.5926C32.355 24.7037 34.225 12.8519 37.03 8.40741C38.9 3.96296 40.77 6.92593 42.64 11.3704C44.51 15.8148 46.38 21.7407 48.25 33.5926C50.12 45.4444 51.99 63.2222 53.86 69.1481C55.73 75.0741 57.6 69.1481 60.405 67.6667C62.275 66.1852 64.145 69.1481 66.015 64.7037C67.885 60.2593 69.755 48.4074 71.625 43.963C73.495 39.5185 75.365 42.4815 77.235 39.5185C79.105 36.5556 80.975 27.6667 83.78 18.7778C85.65 9.88889 87.52 1 89.39 1C91.26 1 93.13 9.88889 95 15.8148C96.87 21.7407 98.74 24.7037 100.61 23.2222C102.48 21.7407 104.35 15.8148 107.155 15.8148C109.025 15.8148 110.895 21.7407 112.765 26.1852C114.635 30.6296 116.505 33.5926 118.375 38.037C120.245 42.4815 122.115 48.4074 123.985 49.8889C125.855 51.3704 127.725 48.4074 130.53 45.4444C132.4 42.4815 134.27 39.5185 136.14 39.5185C138.01 39.5185 139.88 42.4815 141.75 46.9259C143.62 51.3704 145.49 57.2963 147.36 60.2593C149.23 63.2222 151.1 63.2222 153.905 60.2593C155.775 57.2963 157.645 51.3704 159.515 46.9259C161.385 42.4815 163.255 39.5185 165.125 39.5185C166.995 39.5185 168.865 42.4815 170.735 42.4815C172.605 42.4815 174.475 39.5185 177.28 42.4815C183.851 52.8937 187.893 69.451 188.5 81H187.565C186.63 81 184.76 81 182.89 81C181.02 81 179.15 81 177.28 81C174.475 81 172.605 81 170.735 81C168.865 81 166.995 81 165.125 81C163.255 81 161.385 81 159.515 81C157.645 81 155.775 81 153.905 81C151.1 81 149.23 81 147.36 81C145.49 81 143.62 81 141.75 81C139.88 81 138.01 81 136.14 81C134.27 81 132.4 81 130.53 81C127.725 81 125.855 81 123.985 81C122.115 81 120.245 81 118.375 81C116.505 81 114.635 81 112.765 81C110.895 81 109.025 81 107.155 81C104.35 81 102.48 81 100.61 81C98.74 81 96.87 81 95 81C93.13 81 91.26 81 89.39 81C87.52 81 85.65 81 83.78 81C80.975 81 79.105 81 77.235 81C75.365 81 73.495 81 71.625 81C69.755 81 67.885 81 66.015 81C64.145 81 62.275 81 60.405 81C57.6 81 55.73 81 53.86 81C51.99 81 50.12 81 48.25 81C46.38 81 44.51 81 42.64 81C40.77 81 38.9 81 37.03 81C34.225 81 32.355 81 30.485 81C28.615 81 26.745 81 24.875 81C23.005 81 21.135 81 19.265 81C17.395 81 15.525 81 13.655 81C10.85 81 8.98 81 7.11 81C5.24 81 3.37 81 2.435 81H1.5C2.3137 68.1071 3.14406 55.0493 7.11 42.4815Z"
    />
    <path
      d="M188.5 81C187.893 69.451 183.851 52.8938 177.28 42.4815C174.475 39.5185 172.605 42.4815 170.735 42.4815C168.865 42.4815 166.995 39.5185 165.125 39.5185C163.255 39.5185 161.385 42.4815 159.515 46.9259C157.645 51.3704 155.775 57.2963 153.905 60.2593C151.1 63.2222 149.23 63.2222 147.36 60.2593C145.49 57.2963 143.62 51.3704 141.75 46.9259C139.88 42.4815 138.01 39.5185 136.14 39.5185C134.27 39.5185 132.4 42.4815 130.53 45.4444C127.725 48.4074 125.855 51.3704 123.985 49.8889C122.115 48.4074 120.245 42.4815 118.375 38.037C116.505 33.5926 114.635 30.6296 112.765 26.1852C110.895 21.7407 109.025 15.8148 107.155 15.8148C104.35 15.8148 102.48 21.7407 100.61 23.2222C98.74 24.7037 96.87 21.7407 95 15.8148C93.13 9.88889 91.26 1 89.39 1C87.52 1 85.65 9.88889 83.78 18.7778C80.975 27.6667 79.105 36.5556 77.235 39.5185C75.365 42.4815 73.495 39.5185 71.625 43.963C69.755 48.4074 67.885 60.2593 66.015 64.7037C64.145 69.1481 62.275 66.1852 60.405 67.6667C57.6 69.1481 55.73 75.0741 53.86 69.1481C51.99 63.2222 50.12 45.4444 48.25 33.5926C46.38 21.7407 44.51 15.8148 42.64 11.3704C40.77 6.92593 38.9 3.96296 37.03 8.40741C34.225 12.8519 32.355 24.7037 30.485 33.5926C28.615 42.4815 26.745 48.4074 24.875 46.9259C23.005 45.4444 21.135 36.5556 19.265 33.5926C17.395 30.6296 15.525 33.5926 13.655 35.0741C10.85 36.5556 8.98 36.5556 7.11 42.4815C3.14406 55.0493 2.3137 68.1071 1.5 81"
      stroke={active ? main : '#818181'}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <defs>
      <linearGradient id={id || 'paint0'} x1="95" y1="1" x2="95" y2="81" gradientUnits="userSpaceOnUse">
        <stop stopColor={active ? main : '#E6E6E6'} stopOpacity={active ? '30%' : '0%'} />
        <stop offset="100%" stopColor={active ? main : '#E6E6E6'} stopOpacity="0%" />
      </linearGradient>
    </defs>
  </svg>
)
