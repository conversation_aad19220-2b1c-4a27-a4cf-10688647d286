import React from 'react'

export const notFound = ({
  width,
  height,
  text,
  theme: {
    color: {
      primary: { lightest, light, lighter },
      general: { gray1, gray2, light: generalLight },
      status: { success, warning, error },
    },
  },
}) => (
  <svg width={width || 583} height={height || 518} viewBox="0 0 583 518">
    <path
      fill={gray1}
      d="M291.375 518c143.042 0 259-115.958 259-259s-115.958-259-259-259c-143.042 0-259 115.958-259 259s115.958 259 259 259z"
    ></path>
    <path
      fill={gray2}
      d="M537.247 64.75h-491.743c-7.251 0-13.129 5.878-13.129 13.129v296.318c0 7.252 5.878 13.131 13.129 13.131h491.743c7.249 0 13.128-5.879 13.128-13.131v-296.318c0-7.251-5.879-13.129-13.128-13.129z"
    ></path>
    <path fill={gray1} d="M535.771 80.57h-488.773v290.936h488.773v-290.936z"></path>
    <path fill="#B3BBCA" d="M371.788 387.328h-160.829v40.271h160.829v-40.271z"></path>
    <path
      fill="#99A3B5"
      d="M139.523 446.594c-1.669-0.003-3.338 0.003-5.006 0.042h-0.525c-5.285 0-5.285 8.207 0 8.207h301.947c1.366 0 2.736 0.010 4.102 0.019 2.736 0.023 5.471 0.046 8.207-0.019h0.525c5.284 0 5.284-8.207 0-8.207h-5.488c-30.931-21.494-87.387-35.875-151.913-35.875-64.482 0-120.891 14.361-151.85 35.833z"
    ></path>
    <path fill={generalLight} d="M509.068 125.061h-435.37v227.458h435.37v-227.458z"></path>
    <path
      fill={gray2}
      d="M246.050 314.906v-13.556h2.298l6.285 9.551v-9.551h2.298v13.556h-2.298l-6.285-9.551v9.551h-2.298z"
    ></path>
    <path
      fill={gray2}
      d="M267.186 315.201c-1.362 0-2.511-0.295-3.479-0.886s-1.707-1.428-2.232-2.478c-0.525-1.066-0.771-2.297-0.771-3.692s0.263-2.626 0.771-3.692c0.525-1.067 1.264-1.887 2.232-2.478s2.134-0.886 3.479-0.886c1.346 0 2.511 0.295 3.496 0.886s1.706 1.428 2.232 2.478c0.525 1.066 0.771 2.297 0.771 3.692s-0.263 2.626-0.771 3.692c-0.526 1.067-1.264 1.887-2.232 2.478s-2.134 0.886-3.496 0.886zM267.186 313.052c0.902 0 1.674-0.197 2.281-0.608s1.067-0.984 1.362-1.723c0.312-0.739 0.459-1.608 0.459-2.593s-0.148-1.854-0.459-2.593c-0.312-0.739-0.755-1.297-1.362-1.707s-1.362-0.624-2.281-0.624c-0.902 0-1.674 0.197-2.265 0.608-0.607 0.41-1.050 0.984-1.362 1.723s-0.459 1.609-0.459 2.593c0 0.985 0.148 1.854 0.459 2.593 0.295 0.739 0.755 1.297 1.362 1.707s1.362 0.624 2.281 0.624h-0.017z"
    ></path>
    <path
      fill={gray2}
      d="M283.107 314.906v-13.556h4.316c0.115 0 0.344 0 0.673 0s0.656 0.033 0.968 0.066c1.066 0.131 1.985 0.525 2.724 1.149s1.313 1.428 1.69 2.38c0.377 0.952 0.575 2.018 0.575 3.168s-0.197 2.199-0.575 3.167c-0.377 0.968-0.952 1.756-1.69 2.38s-1.658 1.001-2.724 1.149c-0.312 0.033-0.624 0.066-0.968 0.066s-0.558 0-0.673 0h-4.316v0.033zM285.405 312.772h2.018c0.18 0 0.426 0 0.722-0.017s0.558-0.049 0.771-0.082c0.64-0.115 1.165-0.411 1.559-0.853s0.706-1.001 0.886-1.641c0.181-0.64 0.295-1.329 0.295-2.035s-0.099-1.428-0.295-2.068c-0.197-0.64-0.492-1.198-0.902-1.625-0.41-0.443-0.919-0.722-1.543-0.837-0.23-0.049-0.492-0.082-0.771-0.082-0.295 0-0.525 0-0.722 0h-2.018v9.24z"
    ></path>
    <path
      fill={gray2}
      d="M296.858 314.906l4.267-13.556h3.332l4.267 13.556h-2.33l-3.873-12.161h0.508l-3.824 12.161h-2.347zM299.237 311.968v-2.117h7.122v2.117h-7.122z"
    ></path>
    <path fill={gray2} d="M314.436 314.906v-11.422h-4.382v-2.134h11.045v2.134h-4.382v11.422h-2.281z"></path>
    <path
      fill={gray2}
      d="M323.165 314.906l4.266-13.556h3.332l4.267 13.556h-2.331l-3.872-12.161h0.508l-3.823 12.161h-2.346zM325.544 311.968v-2.117h7.123v2.117h-7.123z"
    ></path>
    <path
      fill={lightest}
      d="M287.702 158.638l-56.319 18.319 33.114 101.758 86.869-28.267-23.585-72.492-40.078-19.318z"
    ></path>
    <path fill={lighter} d="M287.704 158.638l40.080 19.318-30.549 9.949-9.53-29.266z"></path>
    <path fill={lightest} d="M509.068 100.805h-435.37v24.255h435.37v-24.255z"></path>
    <path
      fill={error}
      d="M94.852 113.491c0 3.069-2.478 5.547-5.547 5.547s-5.547-2.478-5.547-5.547c0-3.069 2.478-5.547 5.547-5.547s5.547 2.478 5.547 5.547z"
    ></path>
    <path
      fill={warning}
      d="M113.543 113.491c0 3.069-2.478 5.547-5.547 5.547s-5.547-2.478-5.547-5.547c0-3.069 2.478-5.547 5.547-5.547s5.547 2.478 5.547 5.547z"
    ></path>
    <path
      fill={success}
      d="M132.235 113.491c0 3.069-2.478 5.547-5.547 5.547s-5.547-2.478-5.547-5.547c0-3.069 2.478-5.547 5.547-5.547s5.547 2.478 5.547 5.547z"
    ></path>
  </svg>
)
