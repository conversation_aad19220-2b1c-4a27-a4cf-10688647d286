import React from 'react'

export const settings = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M7.29669 3.54654L8.30619 2.09581C8.49313 1.82716 8.79972 1.66699 9.12702 1.66699H10.4575C10.7835 1.66699 11.0891 1.82598 11.2763 2.09299L12.2747 3.51726M7.29669 3.54654C7.00764 3.95892 6.52719 4.16098 6.01724 4.12496M7.29669 3.54654C7.00624 3.95793 6.52719 4.16266 6.01724 4.12496M6.01724 4.12496L4.22815 4.01264C3.9073 3.99249 3.59634 4.12783 3.39242 4.37636L2.55395 5.39824C2.33775 5.66174 2.27065 6.01721 2.37591 6.34139L2.89429 7.93786M2.89429 7.93786C2.9684 8.17814 2.97788 8.43555 2.92485 8.67866M2.89429 7.93786C2.96945 8.17771 2.97858 8.43461 2.9245 8.68115M2.92485 8.67866C2.87111 8.9189 2.75592 9.14574 2.58488 9.32943M2.92485 8.67866L2.9245 8.68115M2.58488 9.32943L1.40742 10.5958C1.17902 10.8415 1.08899 11.1853 1.16767 11.5113L1.46122 12.7278C1.53964 13.0528 1.77529 13.3171 2.08919 13.4321L3.70699 14.025C3.94441 14.1169 4.14951 14.2663 4.30475 14.4544M2.58488 9.32943C2.75697 9.14724 2.87112 8.92113 2.9245 8.68115M4.30475 14.4544C4.45893 14.643 4.56394 14.8707 4.60468 15.1173M4.30475 14.4544C4.46033 14.643 4.56569 14.8701 4.60468 15.1173M4.30475 14.4544L4.30229 14.4517C4.2921 14.4393 4.28122 14.4265 4.27068 14.4145M4.60468 15.1173L4.88021 16.8097C4.93347 17.1369 5.14542 17.4165 5.4461 17.5561L6.64535 18.1129C6.93641 18.248 7.27465 18.2352 7.55471 18.0786L9.07521 17.2279M9.07521 17.2279C9.29612 17.1038 9.54267 17.0416 9.78921 17.0413M9.07521 17.2279C9.16266 17.1781 9.25432 17.1384 9.34845 17.1086C9.49139 17.0633 9.6403 17.041 9.78921 17.0413M9.78921 17.0413C9.9174 17.0415 10.0456 17.0585 10.1706 17.092C10.2869 17.1231 10.4 17.1685 10.5071 17.2279M9.78921 17.0413C10.0368 17.0409 10.2848 17.1032 10.5071 17.2279M10.5071 17.2279L12.102 18.0913C12.3833 18.2436 12.7205 18.2521 13.0091 18.1143L14.2028 17.5441C14.5024 17.401 14.7114 17.1186 14.7607 16.7902L15.0071 15.1486M15.0071 15.1486C15.0461 14.9007 15.1497 14.6713 15.3035 14.481M15.0071 15.1486C15.0447 14.9002 15.149 14.6711 15.3035 14.481M15.3035 14.481C15.4563 14.2926 15.659 14.1426 15.895 14.0498M15.3035 14.481C15.456 14.2919 15.6579 14.1414 15.895 14.0498M15.895 14.0498L17.5321 13.4082C17.8401 13.2875 18.0684 13.0223 18.1421 12.6998L18.4243 11.4643C18.4978 11.1426 18.4079 10.8055 18.184 10.5631L16.9978 9.27863M16.9978 9.27863C16.8236 9.0943 16.702 8.87411 16.643 8.6401M16.9978 9.27863C16.9159 9.19254 16.8457 9.09842 16.7884 8.99863C16.7231 8.88532 16.6743 8.76471 16.643 8.6401M16.643 8.6401C16.5851 8.40777 16.5886 8.16156 16.662 7.92326L17.174 6.28199C17.2744 5.96019 17.206 5.60947 16.9919 5.34908L16.1768 4.35779C15.9706 4.1069 15.6553 3.97182 15.3313 3.99555L13.5647 4.12496M13.5647 4.12496C13.3111 4.14286 13.0618 4.09456 12.8398 3.99065M13.5647 4.12496C13.3108 4.14389 13.0618 4.0952 12.8398 3.99065M12.8398 3.99065C12.6164 3.88541 12.4205 3.72358 12.2747 3.51726M12.8398 3.99065C12.6161 3.88579 12.4198 3.72439 12.2747 3.51726M2.9245 8.68115L2.9252 8.6775M12.3773 10.3143C12.3773 11.8685 11.089 13.1283 9.50017 13.1283C7.9113 13.1283 6.62307 11.8685 6.62307 10.3143C6.62307 8.7602 7.9113 7.50037 9.50017 7.50037C11.089 7.50037 12.3773 8.7602 12.3773 10.3143Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
    />
  </svg>
)
