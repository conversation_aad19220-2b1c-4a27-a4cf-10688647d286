import React from 'react'

export const careDoNotTumbleDry = ({
  width,
  height,
  fill,
  theme: {
    color: {
      primary: { main },
    },
  },
}) => (
  <svg width={width || 22} height={height || 22} viewBox="0 0 22 22" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.93156 16.7413C4.07808 14.9351 4.90902 16.5383 5.60041 17.1917C6.94674 18.4603 7.80623 18.3128 8.5119 19.0328L3.73713 19.0439C2.71747 19.0201 2.93156 18.7124 2.93156 16.7413ZM13.5452 19.0328C13.7148 18.7727 13.7148 18.7489 14.1192 18.557C15.6416 17.8529 16.6295 17.2884 17.627 15.7994C19.273 16.1768 19.2413 17.0823 18.9764 18.944C18.0551 19.166 14.7757 19.028 13.5452 19.028V19.0328ZM5.39585 15.054C5.93977 14.6814 10.6384 11.781 11.0174 11.7969C11.2902 11.8095 16.1538 14.6513 16.5677 15.2094C15.6638 16.7334 13.4611 17.9957 10.8922 17.9941C8.63083 17.9941 5.81767 16.5082 5.39109 15.054H5.39585ZM19.043 14.9446C18.4912 14.9304 18.3628 14.84 18.5118 14.2834C18.5959 13.9662 18.7608 13.6728 19.043 13.5523V14.9446ZM12.3828 10.996C12.5778 10.5742 13.2232 10.3427 13.6038 10.127C13.9844 9.91137 14.3761 9.63861 14.7948 9.40233C15.3942 9.06456 16.7199 8.09882 17.2797 8.05759C18.3739 9.43087 18.1598 13.0845 17.2797 13.925C16.8642 13.8568 15.1627 12.8054 14.7091 12.539C14.208 12.244 12.6476 11.4464 12.3828 10.9929V10.996ZM4.61248 13.9456C3.84021 12.1917 3.61502 9.31511 4.70128 7.99575C5.15481 8.09407 6.68033 9.10579 7.23853 9.42295C7.72536 9.70522 9.44592 10.6044 9.60291 10.9976C9.40786 11.402 4.9899 13.8853 4.61565 13.9472L4.61248 13.9456ZM18.7338 8.15908C18.4167 7.66749 18.1598 7.01415 19.0304 7.0649C19.0614 7.40333 19.0688 7.7435 19.0526 8.08296C18.9812 8.66019 18.9162 8.44294 18.7354 8.15908H18.7338ZM5.41329 6.93169C5.54333 6.07696 6.9071 5.22222 7.54775 4.84164C11.0365 2.77061 15.7351 4.69892 16.5867 6.87619C16.0856 7.23616 11.3536 10.1873 11.0063 10.1921C10.7019 10.1921 5.86207 7.25836 5.40854 6.93169H5.41329ZM17.6571 6.1388C17.3288 5.84385 17.1909 5.55524 16.7643 5.12232C15.4084 3.74745 13.9289 3.41444 13.4627 2.97518C14.7408 2.84039 16.9197 2.91016 18.293 2.91016C18.8591 2.91016 19.0462 2.89113 19.0462 3.46518C19.0462 4.83529 19.2349 5.34274 18.553 5.76615C18.2844 5.95282 17.9789 6.07988 17.6571 6.1388ZM2.92204 3.59363C2.92204 2.99421 3.01877 2.91016 3.61185 2.91016C5.02161 2.91016 7.27024 2.78964 8.57216 2.98945C8.00604 3.43188 6.54871 3.69988 5.09614 5.20954L4.30325 6.17527C2.57793 5.72333 2.92521 4.9685 2.92521 3.5968L2.92204 3.59363ZM1.70258 4.59584C0.962024 4.54668 0.684511 3.81088 0.164376 4.35481C-0.546052 5.09854 1.24746 5.91204 1.72319 6.09916L1.73429 15.8882C1.35947 16.0513 0.996502 16.2405 0.648036 16.4543C0.156446 16.7524 -0.144857 16.93 0.107281 17.5643C0.800266 18.0575 0.998495 17.4914 1.71051 17.3613C1.78187 18.27 1.60426 19.3594 1.8564 20.1476C2.80786 20.3458 19.6472 20.2887 20.1341 20.1158L20.2482 17.42C20.5812 17.3883 22.3097 18.3334 21.9514 17.0489C21.8626 16.7318 20.6621 16.0277 20.234 15.8374V6.16417C20.9888 5.64721 21.9577 5.28882 21.9593 4.73697C21.9593 3.59839 20.651 4.5784 20.2403 4.5784L20.1912 1.84293C16.842 1.60031 6.74377 1.78743 2.71272 1.7906C1.10791 1.7906 1.99436 1.94918 1.69623 4.58791L1.70258 4.59584Z"
      fill={fill || main}
    />
  </svg>
)
