import React from 'react'

export const flowchart = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20">
    <path
      d="M14.8002 10C14.8002 10.8837 15.5165 11.6 16.4002 11.6C17.2839 11.6 18.0002 10.8837 18.0002 10C18.0002 9.11634 17.2839 8.4 16.4002 8.4C15.5165 8.4 14.8002 9.11634 14.8002 10ZM14.8002 10H10.5002M14.8002 3.6C14.8002 4.48366 15.5165 5.2 16.4002 5.2C17.2839 5.2 18.0002 4.48366 18.0002 3.6C18.0002 2.71634 17.2839 2 16.4002 2C15.5165 2 14.8002 2.71634 14.8002 3.6ZM14.8002 3.6H5.4998M14.8002 16.4C14.8002 17.2837 15.5165 18 16.4002 18C17.2839 18 18.0002 17.2837 18.0002 16.4C18.0002 15.5163 17.2839 14.8 16.4002 14.8C15.5165 14.8 14.8002 15.5163 14.8002 16.4ZM14.8002 16.4H10.2002C9.75837 16.4 9.40019 16.0418 9.40019 15.6V4.4M5.1998 3.6C5.1998 4.48366 4.48346 5.2 3.5998 5.2C2.71615 5.2 1.9998 4.48366 1.9998 3.6C1.9998 2.71634 2.71615 2 3.5998 2C4.48346 2 5.1998 2.71634 5.1998 3.6Z"
      stroke={fill || stroke || dark}
      strokeWidth={strokeWidth || 1}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
)
