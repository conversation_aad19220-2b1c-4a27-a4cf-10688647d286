import React from 'react'

export const txtFileType = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
}) => (
  <svg width={width || 16} height={height || 20} viewBox="0 0 16 20" fill="none">
    <g clip-path="url(#clip0_1639_99771)">
      <path
        d="M3.12517 0H9.81183L15.479 5.90705V17.3963C15.479 18.8356 14.3146 20 12.8803 20H3.12517C1.6859 20 0.521484 18.8356 0.521484 17.3963V2.60369C0.521459 1.16441 1.68587 0 3.12517 0Z"
        fill={fill || '#0A183A'}
      />
      <path
        d="M0.84494 2.60369V2.60368C0.844918 1.34306 1.86451 0.323455 3.12517 0.323455H9.6739L15.1555 6.03712V17.3963C15.1555 18.6573 14.1355 19.6765 12.8803 19.6765H3.12517C1.86454 19.6765 0.84494 18.6569 0.84494 17.3963V2.60369Z"
        stroke={stroke || '#172B4D'}
        strokeOpacity={strokeOpacity || '0.1'}
        strokeWidth={strokeWidth || '0.647'}
      />
      <g opacity="0.3" filter="url(#filter0_dd_1639_99771)">
        <path d="M9.80664 0V5.86207H15.4788L9.80664 0Z" fill={fillCorner || 'white'} />
      </g>
      <g>
        <path
          d="M4.29924 14.6027V11.7591H3.25977V10.9695H6.26328V11.7591H5.2288V14.6027H4.29924ZM9.64159 14.6027H8.66708L8.0024 13.4882L7.33773 14.6027H6.35821L7.51265 12.6636L6.50316 10.9695H7.47767L8.0024 11.8491L8.52216 10.9695H9.50167L8.49219 12.6686L9.64159 14.6027ZM10.771 14.6027V11.7591H9.73653V10.9695H12.74V11.7591H11.7006V14.6027H10.771Z"
          fill={colorText || 'white'}
        />
      </g>
    </g>
  </svg>
)
