import React from 'react'

export const docFileType = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
}) => (
  <svg width={width || 16} height={height || 20} viewBox="0 0 16 20" fill="none">
    <g>
      <path
        d="M3.12276 0H9.8146L15.4792 5.90996V17.3982C15.4792 18.8353 14.3145 20 12.8774 20H3.12276C1.68571 20 0.520996 18.8353 0.520996 17.3982V2.60177C0.520996 1.16474 1.68571 0 3.12276 0Z"
        fill={fill || '#0065FF'}
      />
      <path
        d="M3.12276 0.5H9.60126L14.9792 6.11088V17.3982C14.9792 18.5591 14.0383 19.5 12.8774 19.5H3.12276C1.96186 19.5 1.021 18.5591 1.021 17.3982V2.60177C1.021 1.44088 1.96186 0.5 3.12276 0.5Z"
        stroke={stroke || '#172B4D'}
        strokeOpacity={strokeOpacity || '0.1'}
      />
      <g opacity="0.3">
        <path d="M9.80664 0V5.86141H15.4793L9.80664 0Z" fill={fillCorner || 'white'} />
      </g>
      <g>
        <path
          d="M2.93652 14.2707V10.9598H4.10935C4.34391 10.9598 4.56232 10.9949 4.76453 11.0596C4.96674 11.127 5.15007 11.2241 5.31455 11.3535C5.47901 11.4829 5.60844 11.6555 5.7028 11.8712C5.79716 12.0868 5.8457 12.3349 5.8457 12.6153C5.8457 12.8957 5.79716 13.1437 5.7028 13.3594C5.60844 13.5751 5.47901 13.7476 5.31455 13.8771C5.15009 14.0065 4.96674 14.1035 4.76453 14.1709C4.56232 14.2357 4.34393 14.2707 4.10935 14.2707H2.93652ZM3.76424 13.5508H4.00959C4.1417 13.5508 4.26572 13.5347 4.37625 13.505C4.48949 13.4727 4.59193 13.4214 4.68899 13.354C4.78606 13.2866 4.86154 13.1896 4.91546 13.0628C4.97209 12.9388 4.99903 12.7878 4.99903 12.6153C4.99903 12.4427 4.97207 12.2917 4.91546 12.165C4.86154 12.041 4.78606 11.9439 4.68899 11.8765C4.59193 11.8064 4.48949 11.7579 4.37625 11.7255C4.26572 11.6959 4.1417 11.6797 4.00959 11.6797H3.76424V13.5508ZM7.85431 14.3085C7.35553 14.3085 6.94302 14.1467 6.61679 13.8259C6.29055 13.505 6.12877 13.1006 6.12877 12.6153C6.12877 12.13 6.29055 11.7256 6.61679 11.4047C6.94302 11.0839 7.35553 10.9221 7.85431 10.9221C8.345 10.9221 8.75212 11.0839 9.07836 11.4047C9.40189 11.7256 9.56367 12.13 9.56367 12.6153C9.56367 13.1006 9.40189 13.505 9.07836 13.8259C8.75212 14.1467 8.345 14.3085 7.85431 14.3085ZM7.21801 13.3136C7.38247 13.4969 7.59276 13.5886 7.8489 13.5886C8.10504 13.5886 8.31263 13.4969 8.47711 13.3136C8.64157 13.1275 8.72246 12.8957 8.72246 12.6153C8.72246 12.3349 8.64157 12.103 8.47711 11.917C8.31265 11.7336 8.10504 11.642 7.8489 11.642C7.59276 11.642 7.38247 11.7336 7.21801 11.917C7.05355 12.103 6.96996 12.3349 6.96996 12.6153C6.96996 12.8957 7.05355 13.1275 7.21801 13.3136ZM11.5318 14.3085C11.0492 14.3085 10.6475 14.1575 10.3294 13.8609C10.0085 13.5616 9.84947 13.1464 9.84947 12.6153C9.84947 12.0868 10.0112 11.6716 10.3348 11.3723C10.661 11.0731 11.0573 10.9221 11.5319 10.9221C11.9605 10.9221 12.311 11.0272 12.5888 11.2402C12.8638 11.4505 13.0228 11.7309 13.0633 12.0814L12.2275 12.2513C12.1924 12.068 12.1088 11.9197 11.9794 11.8091C11.85 11.6986 11.699 11.642 11.5265 11.642C11.2892 11.642 11.0924 11.7255 10.9333 11.8954C10.7742 12.0679 10.6933 12.3052 10.6933 12.6153C10.6933 12.9253 10.7742 13.1626 10.9306 13.3324C11.0897 13.505 11.2865 13.5886 11.5264 13.5886C11.699 13.5886 11.8473 13.5401 11.9686 13.443C12.0899 13.3459 12.1654 13.2165 12.1978 13.0547L13.0525 13.2489C12.9743 13.5832 12.8017 13.842 12.5321 14.028C12.2652 14.2141 11.9309 14.3085 11.5318 14.3085Z"
          fill={colorText || 'white'}
        />
      </g>
    </g>
  </svg>
)
