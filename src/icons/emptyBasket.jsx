import React from 'react'

export const emptyBasket = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { gray3 },
      primary: { dark, darker, darkest, main, lightest },
    },
  },
}) => (
  <svg width={width || 179} height={height || 164} viewBox="0 0 179 164">
    <path
      d="M134.786 164.001H49.5384C45.5235 164.001 42.2387 160.716 42.2387 156.701L31.8975 91.2294C31.8975 87.2145 35.1823 83.9297 39.1971 83.9297H145.128C149.143 83.9297 152.427 87.2145 152.427 91.2294L142.086 156.701C142.086 160.716 138.801 164.001 134.786 164.001Z"
      fill="url(#paint0_linear_6678_103689)"
    />
    <path
      d="M159.794 92.4187H24.1844C20.1696 92.4187 16.8848 89.1339 16.8848 85.1191V75.8789C16.8848 71.8641 20.1696 69.1875 24.1844 69.1875H159.794C163.809 69.1875 167.094 71.8641 167.094 75.8789V85.1191C167.094 89.1339 163.809 92.4187 159.794 92.4187Z"
      fill={fill || main}
    />
    <path
      opacity="0.1"
      d="M53.7971 156.702L45.5728 104.643H150.305L152.233 92.4219H32.0806L42.2393 156.702C42.2393 160.716 45.5241 164.001 49.539 164.001H61.0968C57.082 164.001 53.7971 160.716 53.7971 156.702Z"
      fill={fill || lightest}
    />
    <path
      opacity="0.2"
      d="M29.2942 69.1875H24.1844C20.1696 69.1875 16.8848 71.8641 16.8848 75.8789V85.1191C16.8848 89.1339 20.1696 92.4187 24.1844 92.4187H29.2942C22.9557 80.8062 29.2942 69.1875 29.2942 69.1875Z"
      fill={fill || lightest}
    />
    <path
      opacity="0.5"
      fillRule="evenodd"
      clipRule="evenodd"
      d="M71.0169 127.389C73.3788 127.389 75.2935 125.475 75.2935 123.113C75.2935 120.751 73.3788 118.836 71.0169 118.836C68.655 118.836 66.7402 120.751 66.7402 123.113C66.7402 125.475 68.655 127.389 71.0169 127.389ZM113.223 127.389C115.585 127.389 117.5 125.475 117.5 123.113C117.5 120.751 115.585 118.836 113.223 118.836C110.862 118.836 108.947 120.751 108.947 123.113C108.947 125.475 110.862 127.389 113.223 127.389ZM83.1033 143.033C83.2675 143.149 83.4557 143.202 83.644 143.202C83.9529 143.202 84.257 143.057 84.4452 142.773C85.5361 141.175 88.8812 140.055 92.2021 140.055C95.523 140.055 98.5832 141.069 99.8093 142.58C100.142 142.995 100.751 143.057 101.166 142.719C101.581 142.386 101.644 141.778 101.306 141.363C99.7031 139.399 96.1312 138.129 92.1972 138.129C87.993 138.129 84.3197 139.524 82.8475 141.691C82.5482 142.131 82.6641 142.734 83.1033 143.033Z"
      fill={fill || darkest}
    />
    <path
      d="M166.991 134.149L166.072 135.068C164.29 136.85 161.37 136.85 159.588 135.068L108.764 84.2438C106.981 82.4614 106.981 79.5415 108.764 77.7592L109.682 76.8407C111.464 75.0583 114.384 75.0583 116.167 76.8407L166.991 127.665C168.773 129.447 168.773 132.367 166.991 134.149Z"
      fill={fill || darker}
    />
    <path
      d="M17.7851 135.068L16.8665 134.149C15.0842 132.367 15.0842 129.447 16.8665 127.665L67.6906 76.8407C69.4729 75.0583 72.3928 75.0583 74.1751 76.8407L75.0937 77.7592C76.876 79.5415 76.876 82.4614 75.0937 84.2438L24.2696 135.068C22.4812 136.85 19.5674 136.85 17.7851 135.068Z"
      fill={fill || darker}
    />
    <path
      d="M153.839 39.9615C153.851 43.0821 151.302 45.6005 148.151 45.5761C145.146 45.5518 142.695 43.0882 142.658 40.0649C142.622 36.926 145.146 34.359 148.255 34.3711C151.315 34.3833 153.827 36.9017 153.839 39.9615ZM148.267 41.7134C149.362 41.6222 149.982 40.9773 149.976 39.9676C149.976 38.9395 149.283 38.3069 148.255 38.2156C147.336 38.1366 146.406 39.1281 146.491 40.0588C146.594 41.1112 147.233 41.6526 148.267 41.7134Z"
      fill={fill || lightest}
    />
    <path
      d="M9.22617 58.8175C9.23834 61.3481 7.16401 63.3981 4.60913 63.3738C2.16982 63.3555 0.180652 61.3542 0.150236 58.8966C0.119821 56.3478 2.16982 54.2613 4.69429 54.2735C7.17618 54.2917 9.22009 56.3357 9.22617 58.8175ZM4.70037 60.241C5.5885 60.168 6.09339 59.6448 6.08731 58.8236C6.08123 57.9902 5.52159 57.4732 4.68821 57.4002C3.93999 57.3333 3.18569 58.1423 3.25868 58.8966C3.34385 59.7543 3.86091 60.1923 4.70037 60.241Z"
      fill={fill || lightest}
    />
    <path
      d="M69.9472 15.1777C65.61 15.1777 62.4468 12.0389 62.4285 7.73206C62.4103 3.43742 65.6343 0.0491481 69.783 0.000483535C73.9985 -0.048181 77.6666 3.58341 77.6058 7.7564C77.5389 11.9476 74.2114 15.1777 69.9472 15.1777ZM70.0993 9.98888C71.395 9.9828 72.1371 9.06425 72.3622 7.7199C72.5508 6.58236 71.2977 5.32317 70.0567 5.25625C68.8401 5.18934 67.6722 6.29646 67.6174 7.56782C67.5687 8.90001 68.5603 9.95238 70.0993 9.98888Z"
      fill={fill || gray3}
    />
    <path
      d="M43.7903 40.6771C42.6771 40.7136 41.6856 39.795 41.6248 38.6757C41.5639 37.5078 42.5372 36.4615 43.6991 36.4493C44.8306 36.4372 45.7552 37.3557 45.7795 38.5176C45.8099 39.6551 44.9035 40.6406 43.7903 40.6771Z"
      fill={fill || lightest}
    />
    <path
      d="M118.004 16.9544C116.891 16.9909 115.899 16.0724 115.838 14.9531C115.777 13.7851 116.751 12.7388 117.912 12.7267C119.044 12.7145 119.969 13.6331 119.993 14.7949C120.023 15.9325 119.117 16.9179 118.004 16.9544Z"
      fill={fill || lightest}
    />
    <path
      d="M178.846 67.3346C178.804 69.4089 176.985 71.1304 174.929 71.027C173.037 70.9297 171.364 69.1108 171.388 67.1642C171.413 65.2481 173.177 63.4962 175.093 63.4962C177.143 63.484 178.877 65.2602 178.846 67.3346Z"
      fill={fill || gray3}
    />
    <path
      d="M98.5499 43.6119C98.5073 45.6862 96.6885 47.4078 94.6324 47.3043C92.7406 47.207 91.0677 45.3882 91.0921 43.4416C91.1164 41.5254 92.8805 39.7735 94.7966 39.7735C96.8466 39.7613 98.5803 41.5376 98.5499 43.6119Z"
      fill={fill || gray3}
    />
    <defs>
      <linearGradient
        id="paint0_linear_6678_103689"
        x1="31.8975"
        y1="123.832"
        x2="152.427"
        y2="123.832"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={dark} />
        <stop offset="1" stopColor={darker} />
      </linearGradient>
    </defs>
  </svg>
)
