import React from 'react'

export const at = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" fill="none">
    <path
      d="M13.1613 17.4194C12.2098 17.8192 11.1003 17.9995 10.0037 18C5.60017 18.002 2.04259 14.482 2.00037 10.0786C1.95773 5.64222 5.57138 2 9.99996 2C14.5 2 18.0422 5.64222 17.9996 10.0786L18 10.2537C18 11.6665 16.8547 12.8572 15.4419 12.8572C14.0291 12.8572 12.8571 11.6665 12.8571 10.2538M9.99996 12.8572C11.5779 12.8572 12.8571 11.5779 12.8571 10C12.8571 8.42208 11.5779 7.14287 9.99996 7.14287C8.42203 7.14287 7.14281 8.42208 7.14281 10C7.14281 11.5779 8.42203 12.8572 9.99996 12.8572Z"
      stroke={stroke || dark}
      strokeWidth={strokeWidth || 1.4}
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
