import React, { useState } from 'react'
import { withKnobs, select, text, boolean } from '@storybook/addon-knobs'

import ExpansionPanel from './ExpansionPanel'
import { LOREM_1, LOREM_HALF } from '../../constants/propTypes'

export default {
  title: 'Organisms/ExpansionPanel',
  component: ExpansionPanel,
}

export const expansionPanel = () => {
  const [activePanelId, setActivePanelId] = useState('')
  return (
    <>
      <ExpansionPanel
        header={text('Header text', LOREM_HALF)}
        headerBackgroundColor={text('Header background color', 'lightgray')}
        headerPadding={text('Header padding', '14px 20px')}
        align={select('Text align', ['inherit', 'left', 'center', 'right', 'justify'], 'inherit')}
        arrowPosition={select('Arrow position', [undefined, 'left', 'right'], 'left')}
        bordered={boolean('Bordered', false)}
        initialOpened={false}
        padding={text('Padding', '20px')}
        marginBottomWhenOpened={text('Margin bottom when opened', '20px')}
        panelName="panelName1"
        activePanelId={activePanelId}
        id="id-1"
        onClick={() => setActivePanelId('id-1')}
      >
        {text('Details text', LOREM_1)}
      </ExpansionPanel>
      <ExpansionPanel
        panelName="panelName2"
        header={text('Header text', LOREM_HALF)}
        headerBackgroundColor={text('Header background color', 'lightgray')}
        headerPadding={text('Header padding', '14px 20px')}
        align={select('Text align', ['inherit', 'left', 'center', 'right', 'justify'], 'inherit')}
        arrowPosition={select('Arrow position', [undefined, 'left', 'right'], 'left')}
        bordered={boolean('Bordered', false)}
        initialOpened={false}
        padding={text('Padding', '20px')}
        marginBottomWhenOpened={text('Margin bottom when opened', '20px')}
        activePanelId={activePanelId}
        id="id-2"
        onClick={() => setActivePanelId('id-2')}
      >
        {text('Details text', LOREM_1)}
      </ExpansionPanel>
  </>
  )
}

expansionPanel.story = {
  decorators: [withKnobs],
}
