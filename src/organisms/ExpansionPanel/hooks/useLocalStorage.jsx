import { useEffect, useMemo, useRef, useState } from 'react'
import { nanoid } from 'nanoid'
import { isClient } from '../../../utils/isClient'

export const PANEL_LS_STATE_KEY = 'expPanelStates'

export const useLocalStorage = ({ panelName, opened, id, setDisplayed, initialOpened, setOpened }) => {
  const [currentPanelStates, setCurrentPanelStates] = useState({})
  const panelId = useRef(id || nanoid())

  useEffect(() => {
    if (isClient && localStorage) {
      setCurrentPanelStates(JSON.parse(localStorage.getItem(PANEL_LS_STATE_KEY)) || {})
    } else {
      setCurrentPanelStates({})
    }
  }, [])

  useEffect(() => {
    if (isClient) {
      const expPanelStates = panelName ? { ...currentPanelStates, [panelName]: opened } : currentPanelStates

      if (localStorage) {
        window.localStorage.setItem(PANEL_LS_STATE_KEY, JSON.stringify(expPanelStates))
        window[`closeExpPanel-${panelId?.current}`] = setTimeout(() => {
          if (!opened) setDisplayed(false)
        }, 300)
      }

      return () => window && clearTimeout(window[`closeExpPanel-${panelId?.current}`])
    }
  }, [opened])

  useEffect(() => {
    const hasLocalStorageState = Boolean(panelName && currentPanelStates?.[panelName])
    const currentState = currentPanelStates?.[panelName] ?? {}

    setOpened(hasLocalStorageState && currentState ? currentState : initialOpened)
    setDisplayed(hasLocalStorageState && currentState ? currentState : initialOpened)
  }, [panelName, currentPanelStates])
}
