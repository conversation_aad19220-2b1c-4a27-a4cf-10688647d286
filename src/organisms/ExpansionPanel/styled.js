'use client'
import styled from 'styled-components'

export const StyledExpansionPanel = styled.div`
  padding: ${({ padding }) => padding};
  transition: margin-bottom 0.3s ease-in-out;
  margin-bottom: 0;

  &.opened {
    margin-bottom: ${({ marginBottomWhenOpened }) => marginBottomWhenOpened};
    //> div:not(.panelHeader):not(:last-child) {
    //  margin-bottom: 20px;
    //}
  }
`

export const StyledHeaderWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: margin-bottom 0.3s ease-in-out;
  margin-bottom: 0;
  background-color: ${({ headerBackgroundColor }) => headerBackgroundColor};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: ${({ headerPadding }) => headerPadding};

  &.opened {
    margin-bottom: 10px;

    .empty & {
      margin-bottom: 0;
    }

    & .headerChevronIcon {
      transform: rotate(180deg);
    }
  }
  &.disabled {
    cursor: default;
  }
`

export const StyledDetailsWrapper = styled.div`
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  &.opened {
    width: 100%;
    opacity: 1;
  }
`
