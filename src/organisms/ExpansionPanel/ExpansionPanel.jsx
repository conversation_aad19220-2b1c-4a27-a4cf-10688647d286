'use client';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledDetailsWrapper, StyledExpansionPanel, StyledHeaderWrapper } from './styled'
import Icon from '../../atoms/Icon'
import Typography from '../../atoms/Typography'
import Badge from '../../atoms/Badge/Badge'
import { PANEL_LS_STATE_KEY, useLocalStorage } from './hooks/useLocalStorage'

const ExpansionPanel = (props) => {
  const {
    className,
    children,
    disabled,
    align,
    header,
    headerBackgroundColor,
    headerIconRightProps = {},
    headerPadding,
    id,
    // headerIconLeftProps,
    initialOpened,
    isHidden,
    headerTypographyProps,
    marginBottomWhenOpened,
    padding,
    panelName,
    withChevronInHeader = true,
    activePanelId = '',
    withBadge,
    facetValues,
    ...otherProps
  } = props
  const ref = useRef(null)
  const [displayed, setDisplayed] = useState(initialOpened)
  const [opened, setOpened] = useState(initialOpened)
  useLocalStorage({ panelName, opened, id, setDisplayed, initialOpened, setOpened })

  useEffect(() => {
    if (activePanelId && id) {
      setDisplayed(id === activePanelId)
      setOpened(id === activePanelId)
    }
  }, [activePanelId, id])

  useEffect(() => {
    if (displayed) setOpened(true)
  }, [displayed])

  const toggleOpened = useCallback(() => {
    if (!opened) setDisplayed(true)
    if (displayed) setOpened(false)
  }, [opened, displayed])

  if (isHidden) {
    return (
      <StyledExpansionPanel
        align={align}
        className={clsx(className, 'expansionPanel')}
        padding={padding}
        {...otherProps}
      >
        {children}
      </StyledExpansionPanel>
    )
  }

  return (
    <StyledExpansionPanel
      marginBottomWhenOpened={marginBottomWhenOpened}
      align={align}
      className={clsx(
        className,
        opened && 'opened',
        'expansionPanel',
        !children || (Array.isArray(children) && !children.length && 'empty')
      )}
      padding={padding}
      {...otherProps}
    >
      <StyledHeaderWrapper
        className={clsx(opened && 'opened', disabled && 'disabled', 'panelHeader')}
        onClick={disabled ? undefined : toggleOpened}
        headerBackgroundColor={headerBackgroundColor}
        headerPadding={headerPadding}
      >
        {/*{headerIconLeftProps && <Icon {...headerIconLeftProps} />}*/}
        <div className="headerContent">
          <Typography type="h4" {...headerTypographyProps}>
            {header}
          </Typography>
          {withBadge && (
            <Badge
              badgeContent={facetValues?.length}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
              className="counterBadge"
              style={{ marginLeft: 20 }}
            />
          )}
        </div>
        {withChevronInHeader && (
          <Icon name="chevronDown" className="headerChevronIcon" strokeWidth={1} {...headerIconRightProps} />
        )}
      </StyledHeaderWrapper>
      {displayed && (
        <StyledDetailsWrapper ref={ref} className={clsx(opened && 'opened', 'detailsWrapper')}>
          {children}
        </StyledDetailsWrapper>
      )}
    </StyledExpansionPanel>
  )
}

export default ExpansionPanel

ExpansionPanel.propTypes = {
  align: T.oneOf(['inherit', 'left', 'center', 'right', 'justify']),
  children: T.node,
  className: T.string,
  disabled: T.bool,
  header: T.oneOfType([T.string, T.object]),
  isHidden: T.bool,
  headerBackgroundColor: T.string,
  // detailsBackgroundColor: T.string,
  marginBottomWhenOpened: T.string,
  withChevronInHeader: T.bool,
  activePanelId: T.string,
}
