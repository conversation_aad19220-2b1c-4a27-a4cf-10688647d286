import React, { useState } from 'react'
import { PropTypes as T } from 'prop-types'
import { withTheme } from 'styled-components'

import { StyledRatingWrapper, StyledRatingWrite } from './styled'
import Typography from '../../atoms/Typography'
import Icon from '../../atoms/Icon'

const RatingWrite = ({ fill, onStarClick, text, theme }) => {
  const [rating, setRating] = useState(null)

  const onRate = (e) => {
    const currRating = e.currentTarget.id
    setRating(currRating)

    if (onStarClick) {
      onStarClick(currRating)
    }
  }

  return (
    <StyledRatingWrapper theme={theme}>
      <Typography variant="button1" text={text} />
      <StyledRatingWrite>
        {[1, 2, 3, 4, 5].map((starId) => {
          const isFilled = starId <= rating

          return (
            <Icon
              key={starId}
              name="starSign"
              fill={isFilled ? '#FFE9C2' : fill}
              width={20}
              height={20}
              id={starId}
              onClick={onRate}
            />
          )
        })}
      </StyledRatingWrite>
    </StyledRatingWrapper>
  )
}

export default withTheme(RatingWrite)

RatingWrite.propTypes = { fill: T.string, theme: T.object }
