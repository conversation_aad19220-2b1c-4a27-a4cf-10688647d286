'use client'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledEditor = styled.div`
  font-family: ${({ systemTheme }) => systemTheme.font.family.primary} !important;

  .ql-snow {
    .ql-picker.ql-header {
      width: 80px;
    }

    .ql-stroke {
      stroke-width: 1;
    }

    polygon.ql-stroke {
      fill: #444;
    }

    .ql-formats {
      margin-right: 10px;
    }
  }

  display: flex;
  flex-direction: column;
  width: 100%;
  z-index: 0;
  border: none !important;

  // Toolbar

  && .ql-toolbar {
    border-color: ${({ systemTheme }) => systemTheme.color.general.gray2};
    font-family: ${({ systemTheme }) => systemTheme.font.family.primary};
    margin: 0;
    padding: 5px 10px;
    border-radius: ${({ systemTheme }) => {
      const radius = systemTheme.size.border.radius.main
      return `${radius} ${radius} 0 0`
    }};

    // Button Group

    & > div {
      margin-right: 10px;
      margin-bottom: 8px;
    }
  }

  // Input

  && .ql-container {
    padding: 12px 15px;
    border-color: ${({ systemTheme }) => systemTheme.color.general.gray2};
    ${({ systemTheme }) => getTokens('input-primary-black-large', systemTheme)};
    border-radius: ${({ systemTheme }) => {
      const radius = systemTheme.size.border.radius.main
      return `0 0 ${radius} ${radius}`
    }};

    .ql-editor {
      padding: 0;
    }

    .public-DraftEditor-content {
      padding: 9px 15px;
    }
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    padding: 3px;
    background: none;
    background-color: ${({ systemTheme }) => systemTheme.color.general.gray1};
    border: 1px solid ${({ systemTheme }) => systemTheme.color.general.gray2};

    &:hover:not(.disabled) {
      background-color: ${({ systemTheme }) => systemTheme.color.general.gray2};
    }
  }

  &,
  > div {
    .secondary & {
    }

    .primary & {
    }

    .inverted & {
    }

    .disabled & {
      background-color: ${({ systemTheme }) => systemTheme.color.general.gray1};
      cursor: not-allowed;
    }

    @media only screen and (min-width: ${({ systemTheme }) => systemTheme.breakpoints.sm}px) {
      .inverted & {
      }
    }
  }

  && > div {
    line-height: 22px;
  }

  & {
    //padding: 10px 6px;
  }
`
