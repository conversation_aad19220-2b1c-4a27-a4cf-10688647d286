import React, { useRef, useState } from 'react'
import ReactQuill from 'react-quill-new'
import { useEffect } from 'react'
import { withTheme } from 'styled-components'

import { StyledEditor } from './styled'
import 'react-quill-new/dist/quill.snow.css'

const TOOLBAR_OPTIONS = [
  [{ header: [1, 2, 3, false] }],
  [
    'bold',
    'italic',
    'underline',
    'strike',
    // 'blockquote', 'link'
  ],
  [{ list: 'ordered' }, { list: 'bullet' }],
  // [{ indent: '-1' }, { indent: '+1' }],
  ['clean'],
]

const RichTextEditor = (
  { theme, onChange, readOnly, placeholder, id, value, toolbarOptions, quillTheme },
  props,
  ref
) => {
  const [currValue, setCurrValue] = useState(value || '')
  const reactQuillRef = useRef(null)

  useEffect(() => {
    setCurrValue(value)
  }, [value])

  const onInputChange = (val) => {
    if (val === '<p><br></p>') return // prevent empty string
    let timeoutId
    setCurrValue(val)

    if (onChange) {
      timeoutId = setTimeout(() => {
        onChange(val.toString('html'))
      })
    }

    return () => clearTimeout(timeoutId)
  }

  return (
    <StyledEditor
      as={ReactQuill}
      id={id}
      ref={ref || reactQuillRef}
      theme={quillTheme || 'snow'}
      placeholder={placeholder}
      modules={{
        toolbar: {
          container: toolbarOptions || TOOLBAR_OPTIONS,
        },
        clipboard: {
          matchVisual: false,
        },
      }}
      formats={['header', 'bold', 'italic', 'underline', 'strike', 'list', 'image', 'link']}
      value={currValue}
      onChange={onInputChange}
      readOnly={readOnly}
      systemTheme={theme}
    />
  )
}

export default withTheme(RichTextEditor)
