'use client'
import styled from 'styled-components'

import Select from '../Select'

// To choose
export const StyledAttributes = styled.div`
  .label {
    margin-bottom: 5px;
  }
  .color {
    cursor: pointer;
    margin-right: 10px;
  }
  .tag {
    padding: 0 2px;
    cursor: pointer;
    margin-right: 10px;
    font-weight: 500;
    border: 1px solid transparent;
    &:hover {
      border: 1px solid ${({ theme }) => theme.color.primary.main};
    }
  }
  .icon {
    .image {
      margin-top: 0;
    }
  }
`

// Chosen
export const StyledCharacteristics = styled.div`
  display: flex;
  align-items: center;
  .tag {
    padding: 0 2px;
  }
  .icon {
    .image {
      margin-top: 0;
      cursor: default;
    }
  }
`

export const StyledCharacteristicsChosen = styled.div`
  display: flex;
  margin-top: 5px;

  &.demandsForm {
    margin-bottom: 12px;
    margin-top: 3px;
    font-size: 14px;
    line-height: 20px;
  }
`

export const StyledCharacteristicsSelect = styled(Select)`
  width: 100%;
`

export const StyledCharacteristicWrapper = styled.div`
  flex-grow: 1;

  .label {
    display: block;
    margin-bottom: 5px;
  }

  .selectWrapper {
    margin-bottom: 10px;
  }
`
