'use client';
import React from 'react'
import ReactTexty from 'react-texty'

import { StyledCharacteristicsChosen } from './styled'

const CategoryCharacteristicsChosen = ({
  characteristicsChosen,
  className,
  language,
  system,
  t,
  unitsPiecesId,
}) => {
  return (
    <StyledCharacteristicsChosen className={className}>
      <ReactTexty>
        (
        {Object.entries(characteristicsChosen)
          .map(([key, char]) => {
            const attributeObject = system.attributes?.[key]

            if (!attributeObject) return null

            const isNumber = attributeObject?.attribute_type === 'numeric'
            const isBoolean = attributeObject?.attribute_type === 'bool'

            const valueText = isNumber
              ? char
              : isBoolean && t
              ? t(char?.id)
              : char?.translations?.[language] || char?.name || ''

            const labelText =
              isNumber || isBoolean ? attributeObject?.translations?.[language] || attributeObject?.name : ''

            const unitObject = isNumber && system.units?.[attributeObject?.unit?.id || unitsPiecesId]
            const unit = unitObject ? ` ${unitObject.translations?.[language] || unitObject.code}` : ''

            return (labelText && `${labelText}: `) + valueText.toLowerCase() + unit
          })
          .join(', ')}
        )
      </ReactTexty>
    </StyledCharacteristicsChosen>
  )
}

export default CategoryCharacteristicsChosen
