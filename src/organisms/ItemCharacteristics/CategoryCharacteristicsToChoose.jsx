'use client';
import React from 'react'

import { StyledAttributes, StyledCharacteristicsSelect, StyledCharacteristicWrapper } from './styled'
import { isObjectEmpty } from '../../utils'
import Input from '../../molecules/Input'
import Typography from '../../atoms/Typography'

// characteristic is an attribute with a flag "is_characteristic" set to true

const CategoryCharacteristicsToChoose = ({
  attributes,
  className,
  enumerations,
  category: item,
  typographyProps = {},
  lng,
  onAttributeChoose,
  selectOpenDirection,
  setAttributesChosen,
  t,
}) => {
  const characteristics = item?.characteristics

  if (
    !characteristics?.length ||
    !attributes ||
    isObjectEmpty(attributes) ||
    !enumerations ||
    isObjectEmpty(enumerations)
  )
    return null

  const onSelectAttribute = (itemId, key) => (value) => {
    if (onAttributeChoose) {
      onAttributeChoose(key, value)
    }

    if (setAttributesChosen) {
      setAttributesChosen((prev) => {
        return { ...prev, [key]: value }
      })
    }
  }

  const onInputAttribute = (itemId, key) => (e) => {
    const value = e.target.value
    if (onAttributeChoose) {
      onAttributeChoose(key, value)
    }

    if (setAttributesChosen) {
      setAttributesChosen((prev) => {
        return { ...prev, [key]: value }
      })
    }
  }

  return (
    <StyledAttributes className={`characteristicsToChoose ${className || ''}`}>
      {characteristics.map((characteristicObj) => {
        const attributeObj = attributes?.[characteristicObj.slug]

        if (!attributeObj) return null

        const isCharacteristicTypeEnumeration = attributeObj?.attribute_type === 'enum'
        const enumId = isCharacteristicTypeEnumeration && attributeObj?.enumeration.id
        let characteristicOptions = enumId && enumerations?.[enumId]?.options

        const isCharacteristicTypeBoolean = attributeObj?.attribute_type === 'bool'
        characteristicOptions = isCharacteristicTypeBoolean
          ? [
              { id: 'yes', name: t ? t('yes') : 'Yes' },
              { id: 'no', name: t ? t('no') : 'No' },
            ]
          : characteristicOptions

        const isCharacteristicTypeNumber = attributeObj?.attribute_type === 'numeric'

        return (
          <StyledCharacteristicWrapper key={characteristicObj.id}>
            <Typography
              text={attributeObj?.translations?.[lng] || attributeObj?.name}
              type="caption1"
              fontWeight={500}
              className="label"
              {...typographyProps}
            />
            {isCharacteristicTypeNumber ? (
              <Input onChange={onInputAttribute(item.id, attributeObj.slug)} withBorder />
            ) : (
              <StyledCharacteristicsSelect
                onChange={onSelectAttribute(item.id, attributeObj.slug)}
                options={characteristicOptions}
                labelKey={[`translations.label.${lng}`, 'name']}
                menuPlacement={selectOpenDirection}
                withBorder
              />
            )}
          </StyledCharacteristicWrapper>
        )
      })}
    </StyledAttributes>
  )
}

export default CategoryCharacteristicsToChoose
