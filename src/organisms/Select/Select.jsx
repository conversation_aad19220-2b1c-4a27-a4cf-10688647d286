'use client';
import clsx from 'clsx'
import { pick } from 'dot-object'
import { PropTypes as T } from 'prop-types'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import ReactSelect, { components } from 'react-select'
import AsyncSelect from 'react-select/async'
import CreatableSelect from 'react-select/creatable'
import { withTheme } from 'styled-components'
import { slugify } from 'transliteration'

import Icon from '../../atoms/Icon'
import Tag from '../../atoms/Tag'
import ClearIndicator from './components/ClearIndicator'
import Control from './components/Control'

import {
  StyledErrorLabel,
  StyledFlexRow,
  StyledLabel,
  StyledOptionRow,
  StyledSelect,
  StyledWrapper,
} from './styled'
import { isObjectEmpty } from '../../utils'

const Select = ({
  autosize,
  className,
  createLabelText = 'Create',
  createOptionPosition = 'first',
  customGetOptionLabel,
  error,
  focusBorderColorKey = 'primary.main',
  focusOutlineColorKey = 'primary.lighter',
  fullWidth,
  getOptionBeforeTextComponent,
  iconName = 'chevronDown',
  iconProps,
  inputValue,
  isClearable,
  isCreatable,
  isDisabled,
  isMulti,
  label,
  labelType = 'top',
  labelKey = 'label',
  labelWidth = '150px',
  loadOptions,
  MultiValue: MultiValueCustom,
  noOptionsMessage,
  onChange,
  onCreateOption,
  onInputChange,
  onOptionClick,
  optionIconColor,
  optionIconKey,
  options,
  placeholder,
  readOnly,
  required,
  value,
  valueKey = 'id',
  variant = 'primary',
  width,
  withBorder,
  withFocusBorder,
  withFocusOutline,
  withTranslation,
  ...otherProps
}) => {
  const isFocusedRef = useRef(false)
  const [customStyles, setCustomStyles] = useState({})

  useEffect(() => {
    const styles = () =>
      autosize && {
        container: (base, state) => {
          isFocusedRef.current = state.isFocused
          return {
            ...base,
            display: 'inline-block',
          }
        },
        placeholder: (base, state) => {
          return {
            ...base,
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'static',
                  top: 'auto',
                  transform: 'none',
                }),
          }
        },
        input: (base, state) => {
          return {
            ...base,
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'absolute',
                  top: '2px',
                  // transform: 'translateY(-40%)',
                }),
          }
        },
        singleValue: (base, state) => {
          return {
            ...base,
            maxWidth: 'none',
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'static',
                  top: 'auto',
                  transform: 'none',
                }),
          }
        },
      }
    setCustomStyles(styles())
  }, [])

  const handleChange = (option) => {
    if (onChange) onChange(option)
  }

  const handleCreate = (inputValue) => {
    // TODO: When we'll have another case of creatable options add necessary values to config (=> this object)
    const newOption = slugify(inputValue) //, __isNew__: true
    if (onCreateOption) onCreateOption({ [valueKey]: newOption, [labelKey]: newOption })
  }

  const DropdownIndicator = () => <Icon name={iconName} className={iconName} {...iconProps} />

  const Option = useCallback(
    (props) => {
      const optionValue = valueKey && props.data ? pick(valueKey, props.data) : props.value
      const optionIcon = props.data.iconName || (optionIconKey && props.data[optionIconKey])

      return (
        <StyledOptionRow
          onClick={onOptionClick && onOptionClick(props)}
          className={optionIcon ? 'withLeftIcon' : ''}
        >
          {optionIcon && <Icon name={optionIcon} height={20} width={20} fill={optionIconColor} />}
          {getOptionBeforeTextComponent && getOptionBeforeTextComponent(props)}
          <components.Option {...props} />
          {value && valueKey && optionValue === pick(valueKey, value) && (
            <Icon className="rightIcon" name="check" />
          )}
        </StyledOptionRow>
      )
    },
    [valueKey, optionIconKey, onOptionClick, optionIconColor, getOptionBeforeTextComponent, value]
  )

  const MultiValue = (props) => {
    return (
      <components.MultiValueContainer {...props}>
        <Tag type="gray" removeProps={props.removeProps} withCrossIcon>
          <components.MultiValueLabel {...props} />
        </Tag>
      </components.MultiValueContainer>
    )
  }

  const getOptionLabel = (option) => {
    const hasSpareLabelKeyToCheck = typeof labelKey !== 'string'
    const firstLabelValue = pick(hasSpareLabelKeyToCheck ? labelKey[0] : labelKey, option)

    return hasSpareLabelKeyToCheck ? firstLabelValue || pick(labelKey[1] || '', option) : firstLabelValue
  }

  const filterOptions = (candidate, input) => {
    if (!input) {
      return true
    }

    if (withTranslation && typeof labelKey === 'string') {
      const labelKeyWithoutLng = labelKey.split('.').slice(0, -1).join('.')
      const translationsObject = pick(labelKeyWithoutLng, candidate.data)

      if (!isObjectEmpty(translationsObject)) {
        return Object.values(translationsObject).some((value) =>
          value.toLowerCase().includes(input.toLowerCase())
        )
      }
      return getOptionLabel(candidate)?.toLowerCase().includes(input.toLowerCase())
    }
  }

  const getSelect = useCallback(
    () => (
      <StyledWrapper
        className={clsx(!label && className, 'selectWrapper', isDisabled && 'disabled')}
        fullWidth={fullWidth}
        width={width}
      >
        <StyledSelect
          as={isCreatable ? CreatableSelect : loadOptions ? AsyncSelect : ReactSelect}
          inputValue={inputValue}
          onInputChange={onInputChange}
          focusBorderColorKey={focusBorderColorKey}
          focusOutlineColorKey={focusOutlineColorKey}
          loadOptions={loadOptions}
          // autoFocus
          variant={variant}
          // defaultInputValue
          // isSearchable
          // defaultValue=
          styles={customStyles}
          className={clsx(
            'select',
            labelType === 'top' && 'labelTop',
            !!error && 'hasError',
            autosize && 'autosize',
            withFocusBorder && 'withFocusBorder',
            withFocusOutline && 'withFocusOutline'
          )}
          createOptionPosition={createOptionPosition}
          formatCreateLabel={(inputValue) => {
            return `${createLabelText} ${slugify(inputValue)}`
          }}
          getNewOptionData={(inputValue, optionLabel) => {
            // TODO: When we'll have another case of creatable options add necessary values to config (=> this object)
            return { [valueKey]: inputValue, [labelKey]: optionLabel }
          }}
          noOptionsMessage={() => (isCreatable ? null : noOptionsMessage)}
          onCreateOption={handleCreate}
          filterOption={
            isCreatable
              ? (candidate, input) => {
                  return getOptionLabel(candidate)?.includes(slugify(input))
                }
              : filterOptions
          }
          value={value}
          onChange={handleChange}
          options={options}
          classNamePrefix="react-select"
          closeMenuOnSelect={!isMulti}
          isClearable={isClearable}
          isMulti={isMulti}
          isDisabled={isDisabled || readOnly}
          components={{
            Control,
            DropdownIndicator,
            Option,
            ClearIndicator,
            MultiValue: MultiValueCustom || MultiValue,
          }}
          placeholder={placeholder || ''}
          withBorder={withBorder}
          getOptionLabel={
            customGetOptionLabel
              ? (option) => customGetOptionLabel(option, otherProps.t, otherProps.lng)
              : labelKey && getOptionLabel
          }
          getOptionValue={valueKey && ((option) => option[valueKey])}
          {...otherProps}
        />

        {!!error && <StyledErrorLabel>{error}</StyledErrorLabel>}
      </StyledWrapper>
    ),
    [value, options, placeholder, error, inputValue]
  )

  return label ? (
    <StyledFlexRow className={clsx(className, labelType === 'top' && 'labelTop', 'wrapper')}>
      <StyledLabel
        variant={variant}
        text={label}
        labelWidth={labelWidth}
        className={clsx('label', required && 'required')}
      />
      {getSelect()}
    </StyledFlexRow>
  ) : (
    getSelect()
  )
}

export default withTheme(Select)

Select.propTypes = {
  createLabelText: T.string,
  createOptionPosition: T.oneOf(['first', 'last']),
  isDisabled: T.bool,
  focusBorderColorKey: T.string,
  focusOutlineColorKey: T.string,
  fullWidth: T.bool,
  iconName: T.string,
  label: T.string,
  labelKey: T.oneOfType([T.string, T.array]),
  labelType: T.oneOf(['top', 'left']),
  labelWidth: T.string,
  name: T.string,
  onChange: T.func,
  placeholder: T.string,
  primaryFocusColor: T.bool,
  valueKey: T.string,
  variant: T.oneOf(['primary', 'secondary', 'transparent']),
  width: T.string,
  withBorder: T.bool,
  withFocusBorder: T.bool,
  withFocusOutline: T.bool,
}
