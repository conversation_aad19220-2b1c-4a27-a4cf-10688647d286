import React from 'react'
import { components } from 'react-select'

import Icon from '../../../atoms/Icon'

const ClearIndicator = ({ children, ...props }) => {
  const { isDisabled, error, theme, strokeWidth } = props.selectProps

  return (
    <components.ClearIndicator {...props}>
      <Icon
        width={12}
        height={12}
        name="cross"
        className="crossIcon"
        fill={
          isDisabled ? theme.color.general.gray2 : error ? theme.color.status.error : theme.color.general.dark
        }
        strokeWidth={strokeWidth}
      />
    </components.ClearIndicator>
  )
}

export default ClearIndicator
