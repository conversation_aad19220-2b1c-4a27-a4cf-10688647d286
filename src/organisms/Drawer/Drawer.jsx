'use client';
import React, { forwardRef, useCallback, useEffect, useState } from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledDrawer } from './styled'
import Icon from '../../atoms/Icon'
import { useUnmount } from '../../../build/hooks/useReact'
import FlexRow from '../../atoms/FlexRow'
import Typography from '../../atoms/Typography'

const Drawer = forwardRef(
  (
    {
      absolutePositioned = false,
      backIconProps = {},
      backIconPadding = '20px',
      children,
      className,
      closeIconPadding = '20px',
      closeIconProps = {},
      destroyOnClose = true,
      id,
      isStatic = false,
      headerHeight = '0px',
      labelProps,
      onClose,
      openedValue,
      side = 'right',
      title,
      toggleDrawer,
      width,
      withCloseIcon = true,
      withTitleSection = true,
      withBackIcon = false,
      isRightPanelWide,
      withRightPanelWideIcon,
      onToggleIconClickPanelWide,
      isRightPanelWideProps = {},
    },
    ref
  ) => {
    const [displayed, setDisplayed] = useState(isStatic || !destroyOnClose || false)
    const [opened, setOpened] = useState(isStatic || false)

    const closeDrawer = useCallback(() => {
      if (destroyOnClose) {
        setOpened(false)
        clearTimeout(window[`${id}-openId`])
        window[`${id}-closeId`] = setTimeout(() => {
          setDisplayed(false)
          if (onClose) {
            onClose()
          }
        }, 350)
      } else {
        setOpened(false)
        if (onClose) {
          onClose()
        }
      }
      if (toggleDrawer) {
        toggleDrawer(false)
      }
    }, [destroyOnClose, onClose, toggleDrawer])

    const openDrawer = useCallback(() => {
      if (destroyOnClose) {
        setDisplayed(true)
        clearTimeout(window[`${id}-closeId`])
        window[`${id}-openId`] = setTimeout(() => {
          setOpened(true)
          if (toggleDrawer) {
            toggleDrawer(true)
          }
        }, 100)
      } else {
        setOpened(true)
        if (toggleDrawer) {
          toggleDrawer(true)
        }
      }
    }, [destroyOnClose, toggleDrawer])

    useEffect(() => {
      if (openedValue === opened) {
        return
      }
      if (openedValue) {
        openDrawer()
      } else if (openedValue === false) {
        closeDrawer()
      }
    }, [openedValue, closeDrawer, openDrawer])

    useUnmount(() => {
      clearTimeout(window[`${id}-closeId`])
      clearTimeout(window[`${id}-openId`])
    })

    return (
      displayed && (
        <StyledDrawer
          absolutePositioned={absolutePositioned}
          closeIconPadding={closeIconPadding}
          className={clsx(
            className,
            'drawer',
            opened && 'opened',
            displayed && 'displayed',
            side,
            isStatic && 'static'
          )}
          headerHeight={headerHeight}
          ref={ref}
          width={width}
        >
          {withTitleSection && (
            <FlexRow justifyContent="space-between" className="titleRow">
              {withBackIcon && (
                <Icon
                  name="chevronDoubleLeft"
                  className="backIcon"
                  width={20}
                  height={20}
                  wrapperWidth={36}
                  wrapperHeight={36}
                  size={20}
                  strokeWidth={1}
                  {...backIconProps}
                />
              )}
              {withRightPanelWideIcon && (
                <Icon
                  name="navMenuToggleArrow"
                  className={`toggleIcon ${isRightPanelWide ? 'open' : ''}`}
                  width={20}
                  height={20}
                  wrapperWidth={36}
                  wrapperHeight={36}
                  size={20}
                  strokeWidth={1}
                  onClick={onToggleIconClickPanelWide}
                  {...isRightPanelWideProps}
                />
              )}
              {typeof title === 'string' ? <Typography type="h3" text={title} {...labelProps} /> : title}
              {withCloseIcon && (
                <Icon
                  name="cross"
                  onClick={closeDrawer}
                  className="closeIcon"
                  width={20}
                  height={20}
                  wrapperWidth={36}
                  wrapperHeight={36}
                  size={20}
                  strokeWidth={1}
                  {...closeIconProps}
                />
              )}
            </FlexRow>
          )}
          {children}
        </StyledDrawer>
      )
    )
  }
)

export default Drawer

Drawer.propTypes = {
  absolutePositioned: T.bool,
  closeIconPadding: T.string,
  headerHeight: T.string,
  destroyOnClose: T.bool,
  isStatic: T.bool,
  side: T.oneOf(['left', 'right']),
  width: T.string,
  withCloseIcon: T.bool,
  withTitleSection: T.bool,
}
