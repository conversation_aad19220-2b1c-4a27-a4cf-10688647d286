import React from 'react'
import { withTheme } from 'styled-components'
import parse from 'html-react-parser'

import Typography from '../../atoms/Typography'
import { StyledTextPage } from './styled'
import ErrorBoundary from '../../ErrorBoundary'

const TextPage = ({ header, pageText, children }) => {
  return (
    <>
      <ErrorBoundary>
        <StyledTextPage className="textPage">
          <Typography type="h1" margin="0 0 50px" textAlign="center">
            {header}
          </Typography>
          {parse(pageText)}
        </StyledTextPage>
      </ErrorBoundary>
      {children}
    </>
  )
}

export default withTheme(TextPage)
