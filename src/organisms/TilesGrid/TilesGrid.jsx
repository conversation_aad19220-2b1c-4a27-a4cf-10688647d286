'use client';
import React, { useContext, useEffect, useState } from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledTilesGrid } from './styled'
import { ALIGN_JUSTIFY_CONTENT_TYPES, ALIGN_JUSTIFY_ITEMS_TYPES } from '../../types'

const TilesGrid = ({
  alignContent = 'center',
  alignItems = 'center',
  currentBreakpoint,
  children,
  className,
  itemsInRow = 1,
  justifyContent = 'center',
  justifyItems = 'center',
  rowGap,
  tiles = [],
}) => {
  const [itemsInRowCount, setItemsInRowCount] = useState(itemsInRow)

  const isTablet = currentBreakpoint === 'md'
  const isLandscape = currentBreakpoint === 'sm'
  const isMobile = currentBreakpoint === 'xs'
  const isXMobile = currentBreakpoint === 'xxs'

  useEffect(() => {
    setItemsInRowCount(itemsInRow)
  }, [itemsInRow])

  useEffect(() => {
    if (!currentBreakpoint) {
      return
    }
    if (isMobile || isXMobile || isLandscape) {
      setItemsInRowCount(1)
    } else if (isTablet) {
      setItemsInRowCount(2)
    } else {
      setItemsInRowCount(itemsInRow)
    }
  }, [currentBreakpoint, itemsInRow])

  return (
    <StyledTilesGrid
      itemsInRow={itemsInRowCount}
      justifyItems={justifyItems}
      alignItems={alignItems}
      justifyContent={justifyContent}
      alignContent={alignContent}
      rowGap={rowGap}
      className={clsx(className, 'tilesGrid')}
    >
      {(children || tiles).map((tile, i) => (
        <div key={i} className="tilesGridItem">
          {tile}
        </div>
      ))}
    </StyledTilesGrid>
  )
}

TilesGrid.propTypes = {
  alignContent: T.oneOf(ALIGN_JUSTIFY_CONTENT_TYPES),
  alignItems: T.oneOf(ALIGN_JUSTIFY_ITEMS_TYPES),
  className: T.string,
  currentBreakpoint: T.string,
  itemsInRow: T.number,
  justifyContent: T.oneOf(ALIGN_JUSTIFY_CONTENT_TYPES),
  justifyItems: T.oneOf(ALIGN_JUSTIFY_ITEMS_TYPES),
  rowGap: T.string,
  tiles: T.array,
}

export default TilesGrid
