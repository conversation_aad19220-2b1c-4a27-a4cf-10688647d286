'use client';
import { PropTypes as T } from 'prop-types'
import React, { useState } from 'react'
import { withTheme } from 'styled-components'

import Badge from '../../atoms/Badge/Badge'
import FlexRow from '../../atoms/FlexRow/FlexRow'
import Line from '../../atoms/Line/Line'
import Typography from '../../atoms/Typography'
import Button from '../../molecules/Button'
import Checkbox from '../../molecules/Checkbox'
import { isObject } from '../../utils'
import { getFilterLabels } from '../../utilsDataRelated/filters'
import { getAvailableTranslation } from '../../utilsDataRelated/translations'
import ExpansionPanel from '../ExpansionPanel'
import TagsPanel from '../TagsPanel'
import Icon from '../../atoms/Icon'
import Input from '../../molecules/Input'

import {
  StyledCheckboxList,
  StyledFilter,
  StyledFilters,
  StyledFiltersTitle,
  StyledLonelyCheckbox,
  StyledLink,
} from './styled'

const Filters = ({
  buttonProps,
  chosenTopPlaced,
  className,
  config,
  currentLng,
  iconVariant,
  header,
  headerProps,
  fallbackLng,
  filters,
  fnDispatch,
  fnFilterAdd,
  fnFiltersClearAll,
  fnFilterRemove,
  fnNavigate,
  labelProps,
  locationSearch,
  padding,
  panelColorKey,
  searchItemsLength,
  showTitle,
  systemData,
  tagProps,
  t,
  theme,
  variant = 'secondary',
  withTagsPanel,
  withBadge,
}) => {
  const { active, labels, facets } = filters
  const facetsKeysFiltered = Object.keys(facets)

  if (!facetsKeysFiltered?.length) {
    return null
  }

  const onCheckboxClick = (checked, key, value) => {
    if (checked) {
      fnDispatch(fnFilterAdd(key, value, currentLng, locationSearch, fnNavigate))
    } else {
      fnDispatch(fnFilterRemove(key, value, currentLng, locationSearch, fnNavigate))
    }
  }

  const addLabelsToLabels = (labels) =>
    labels.map((label) => {
      const [collection, id] = label.split(':')
      const filterLabel = getFilterLabels(config.options || systemData[config[collection].key], id)
      return { id: label, label: filterLabel }
    })

  const onTagRemove = (tag) => () => {
    const [key, value] = tag.split(':')
    fnDispatch(fnFilterRemove(key, value, currentLng, locationSearch, fnNavigate))
  }

  const getLabel = (value, lng) =>
    isObject(value.label) ? getAvailableTranslation(value.label, fallbackLng, lng) : value.label || value.id

  const getFilter = (filtersConfig, facetId, facetValues, i) => {
    const filter = filtersConfig[facetId] || {}

    const [showAll, setShowAll] = useState(false)
    const [searchQuery, setSearchQuery] = useState('')
    const maxVisible = 10

    if (!facetValues?.length) {
      return null
    }

    const filteredFacets = facetValues.filter((value) => {
      if (!value?.label) return false

      const labelText = isObject(value.label)
        ? getAvailableTranslation(value.label, fallbackLng, currentLng)
        : value.label

      return typeof labelText === 'string' && labelText.toLowerCase().includes(searchQuery.toLowerCase())
    })

    const visibleFacets = showAll ? filteredFacets : filteredFacets.slice(0, maxVisible)

    if (filter.type === 'checkboxList') {
      return (
        <React.Fragment key={i}>
          <ExpansionPanel
            key={i}
            header={filter.label[currentLng] || filter.label[fallbackLng]}
            initialOpened
            padding="15px 20px"
            headerTypographyProps={headerProps}
            withBadge={withBadge}
            facetValues={facetValues}
          >
            <StyledFilter>
              {facetValues.length > maxVisible && (
                <Input
                  placeholder={t('forms:search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  iconLeftProps={{
                    name: 'search',
                    strokeWidth: 1,
                    width: 20,
                    height: 20,
                    fill: theme.color.general.gray3,
                    style: { left: 10 },
                  }}
                  fullWidth
                  withBorder
                  style={{ height: '32px' }}
                />
              )}
              <StyledCheckboxList>
                {visibleFacets.map((value, id) => {
                  return value?.id ? (
                    <FlexRow justifyContent="space-between" alignItems="center" key={id}>
                      <Checkbox
                        checked={active[facetId]?.includes(value.id) || false}
                        handleChange={(e) => onCheckboxClick(e, facetId, value.id)} //{ id: value.id, slug: slugify(value.label?.en) }
                        label={`${getLabel(value, currentLng)}`}
                        value={value.id}
                        variant={variant}
                        labelType="link"
                        labelProps={labelProps}
                        iconVariant={iconVariant}
                        name={id}
                      />
                      <Badge
                        badgeContent={searchItemsLength ? value.count : 0}
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                        className="counterBadge"
                        style={{ left: '-13px' }}
                      />
                    </FlexRow>
                  ) : null
                })}
              </StyledCheckboxList>
              {facetValues.length > maxVisible && (
                <StyledLink onClick={() => setShowAll(!showAll)}>
                  <Typography
                    text={showAll ? t('showLess') : t('showMore')}
                    color={theme.color.primary.main}
                    fontWeight={500}
                    lineHeight="16px"
                  />
                  <Icon
                    name="chevronDown"
                    className={showAll ? 'showLess' : 'showMore'}
                    fill={theme.color.primary.main}
                  />
                </StyledLink>
              )}
            </StyledFilter>
          </ExpansionPanel>
          <Line color={theme.color.general.gray1} margin="0" />
        </React.Fragment>
      )
    } else if (filter.type === 'checkbox') {
      return (
        <StyledLonelyCheckbox
          checked={active[facetId]?.includes(facetValues)}
          handleChange={(e) => onCheckboxClick(e, facetId, facetValues)}
          key={i}
          label={filter.label[currentLng] || filter.label[fallbackLng]}
          order={filters.length}
          variant={variant}
          name={facetId}
        />
      )
    }
  }

  return (
    <StyledFilters className={className} chosenTopPlaced={chosenTopPlaced} padding={padding}>
      {showTitle && (
        <StyledFiltersTitle>
          <Typography type="h4" className="title" text={header} />
        </StyledFiltersTitle>
      )}
      {/**
       <ExpansionPanel key={-1}  header={t('price')} size="small" initialOpened>
       <StyledPriceWrapper>
       <Slider max={100} min={0} className="priceSlider" variant={variant} />
       </StyledPriceWrapper>
       </ExpansionPanel>
       */}

      {facetsKeysFiltered.map((facet, i) => getFilter(config, facet, facets[facet], i))}

      {withTagsPanel && !!labels.length && (
        <div className="tagsStickyWrapper">
          <TagsPanel
            panelColorKey={panelColorKey}
            removeTag={onTagRemove}
            tags={addLabelsToLabels(labels)}
            currentLanguage={currentLng}
            className="chosenFilters"
            withButton
            tagProps={tagProps}
            buttonProps={{
              text: t('clearFilters'),
              onClick: () => fnDispatch(fnFiltersClearAll(currentLng, locationSearch, fnNavigate)),
              ...buttonProps,
            }}
          />
        </div>
      )}

      {!withTagsPanel && Object.keys(active).length > 0 && (
        <Button
          className="resetButton"
          variant="bordered"
          text={t('clearFilters')}
          onClick={() => fnDispatch(fnFiltersClearAll(currentLng, locationSearch, fnNavigate))}
          {...buttonProps}
        />
      )}
    </StyledFilters>
  )
}

export default withTheme(Filters)

Filters.propTypes = {
  chosenTopPlaced: T.bool,
  config: T.object,
  currentLng: T.string.isRequired,
  filters: T.object,
  padding: T.string,
  systemData: T.object,
  t: T.func.isRequired,
  variant: T.oneOf(['primary', 'secondary']),
  withTagsPanel: T.bool,
}
