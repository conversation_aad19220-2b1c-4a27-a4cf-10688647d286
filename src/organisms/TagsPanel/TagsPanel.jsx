import React from 'react'

import { StyledTagsPanel } from './styled'
import Tag from '../../atoms/Tag'
import Button from '../../molecules/Button'

const TagsPanel = ({
  buttonProps = {
    text: 'Button',
  },
  panelColorKey = 'gray2',
  children,
  className,
  currentLanguage,
  removeTag = () => {},
  tags,
  tagProps,
  withButton,
}) => (
  <StyledTagsPanel className={className} panelColorKey={panelColorKey}>
    {children ||
      tags.map((tag, i) => (
        <Tag key={i} withCrossIcon onCrossClick={removeTag(tag?.id || tag)} {...tagProps}>
          {tag?.label?.[currentLanguage] || tag?.label || tag?.id || tag}
        </Tag>
      ))}
    {withButton && <Button variant="primary" fullWidth {...buttonProps} />}
  </StyledTagsPanel>
)

export default TagsPanel
