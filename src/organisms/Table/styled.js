'use client'
import BaseTable from 'react-base-table'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledTableWrapper = styled.div`
  .BaseTable--empty {
    .BaseTable__header {
      display: none;
    }

    .BaseTable__empty-layer {
      top: 15px !important;
      background-color: transparent;
      overflow: visible;
    }
  }
`

export const StyledBaseTable = styled(BaseTable)`
    box-shadow: none;

    &.containerNotFound {
        .BaseTable__empty-layer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }

    .BaseTable__table.BaseTable__table-main {
        outline: none;
        border: none;
        padding: 0;

        .BaseTable__body {
            overflow: visible !important;
        }

        .BaseTable__header-cell .typography {
            ${({ theme, headerTypographyType }) =>
                    getTokens(`typography-${headerTypographyType}-black-large`, theme)};
        }

        .BaseTable__row {
            border-bottom: 1px solid ${({ theme: { color } }) => color.general.gray1};

            &:hover {
                background-color: ${({ theme: { color } }) => color.general.gray1};
            }

            &.activeRow {
                background-color: ${({ theme: { color } }) => color.general.gray2};
            }

            .BaseTable__row-cell-text {
                ${({ theme, cellTypographyType }) =>
                        getTokens(`typography-${cellTypographyType}-black-large`, theme)};
            }
        }

        .BaseTable__row-cell {
            overflow: initial !important;
        }

        .BaseTable__header-cell:first-child,
        .BaseTable__row-cell:first-child {
            padding-left: 0;
            margin-left: 15px;
        }
    }

    .BaseTable__row-cell {
        & > div[data-texty]:not(.BaseTable__row-cell-text) {
            ${({ theme, cellTypographyType }) =>
                    getTokens(`typography-${cellTypographyType}-black-large`, theme)};
        }
    }

    .BaseTable__header-row {
        background-color: ${({ theme: { color } }) => color.general.light};
        border-radius: ${({ theme: { size } }) => size.border.radius.main};
        border-top: 1px solid ${({ theme: { color } }) => color.general.gray1};

        .BaseTable__header-cell--sortable:hover {
            background-color: ${({ theme: { color } }) => color.general.gray1};
        }

        .BaseTable__header-cell--sortable.BaseTable__header-cell--sorting {
            .BaseTable__sort-indicator {
                svg path {
                    fill: ${({ theme: { color } }) => color.general.gray5};
                }
            }
        }

        .BaseTable__sort-indicator {
            display: flex !important;
            margin-left: 4px;

            svg path {
                fill: ${({ theme: { color } }) => color.general.gray3};
            }
        }
    }

    .BaseTable--empty .BaseTable__table-main .BaseTable__header {
        display: none;
    }

    &.withSummary {
        .BaseTable__table-main {
            padding-bottom: ${({ headerHeight }) => headerHeight + 20}px;
        }
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
        .BaseTable__table-main {
            padding: 10px;
        }
    }
`

export const StyledTableSummary = styled.div`
  position: absolute;
  bottom: ${({ headerHeight }) => -headerHeight + 2}px;
  left: 20px;
  right: 20px;
  height: ${({ headerHeight }) => headerHeight}px;
  padding: 0;
  display: flex;
  align-items: center;
  background-color: ${({ theme: { color } }) => color.general.gray2};
  border-radius: ${({ theme: { size } }) => size.border.radius.main};
  font-size: 13px;
  font-weight: 600;

  .summaryTitle {
    margin-right: auto;
    padding-left: 7px;
  }

  .summaryLabel {
    //margin-left: 20px;
    margin-right: 5px;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
    .expansionPanel & {
      .opened & {
        left: 10px;
        right: 10px;
      }
    }
  }
`

export const StyledSummaryCell = styled.div`
  width: ${({ width }) => width}px;
  flex-grow: ${({ flexGrow }) => flexGrow};
  padding: ${({ padding }) => padding || '0 7px'};
`

export const StyledEmptyTable = styled.div`
  padding: 0 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
`
