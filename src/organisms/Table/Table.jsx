'use client';
import React from 'react'
import { AutoResizer } from 'react-base-table'
import ReactTexty from 'react-texty'
import { withTheme } from 'styled-components'

import 'react-base-table/styles.css'
import 'react-texty/styles.css'

import ErrorBoundary from '../../ErrorBoundary'
import UiLink from '../../atoms/UiLink'
import SelectableTable from './SelectableTable'
import EditableCell from './components/EditableCell'
import EmptyTable from './components/Empty'
import SortIndicator from './components/SortIndicator'
import TableSummary from './components/TableSummary'
import Typography from '../../atoms/Typography'
import Icon from '../../atoms/Icon'
import { StyledBaseTable, StyledTableWrapper } from './styled'

const Table = ({
  cellTypographyType = 'body1',
  children,
  columns,
  components,
  customBlockValues,
  data,
  emptyText,
  emptyIconProps = {},
  headerHeight = 48,
  headerTypographyType = 'button2',
  isEmptyTextOnly,
  isReadOnly,
  maxHeight,
  onBlurCellInput,
  // onChangeCellInput,
  onColumnSort,
  onResize,
  onRowClick,
  // onSubmitCellInput,
  paddingsAndBorders = 0,
  rowHeight,
  rowClassName,
  rowKey,
  selectable,
  sortBy,
  summaryData,
  theme,
  // topHeight,
  isAllSelected,
  isSelectAllDisabled,
  useRowKeyInEditCellName,
  containerNotFound,
  blockKey,
  ...rest
}) => {
  const TableCell = (props) => {
    const { className, cellData, column } = props
    if (column.cellRenderer) {
      return column.cellRenderer()
    }

    if (column.isLink) {
      return (
        <UiLink href={cellData?.includes('https') ? cellData : `https://${cellData}`} key={column.key}>
          <ReactTexty className={className}>{cellData}</ReactTexty>
        </UiLink>
      )
    }

    if (!isReadOnly && column.isEditable) {
      return (
        <EditableCell
          // value={editedValues[rowData[rowKey]]}
          column={column}
          onBlur={onBlurCellInput}
          // onChange={onChangeCellInput}
          // onSubmit={onSubmitCellInput}
          rowKey={rowKey}
          useRowKeyInName={useRowKeyInEditCellName}
          {...props}
        />
      )
    }

    return (
      <ReactTexty key={column.key} className={className}>
        {cellData === 0 ? '0' : cellData}
      </ReactTexty>
    )
  }

  return (
    <AutoResizer onResize={onResize}>
      {({ width, height }) => {
        return (
          <>
            <StyledTableWrapper>
              <StyledBaseTable
                as={selectable && !isReadOnly && SelectableTable}
                cellTypographyType={cellTypographyType}
                className={`${summaryData && !!data?.length ? 'withSummary' : ''} ${
                  containerNotFound ? 'containerNotFound' : ''
                }`}
                columns={columns}
                components={{
                  TableCell,
                  SortIndicator,
                  ...components,
                }}
                data={data}
                emptyRenderer={
                  isEmptyTextOnly || blockKey.endsWith('_items') ? (
                    <EmptyTable text={emptyText}>
                      <Icon
                        name="notAdded"
                        width="56px"
                        height="56px"
                        wrapperWidth="100%"
                        wrapperHeight="60%"
                        {...emptyIconProps}
                      />
                    </EmptyTable>
                  ) : (
                    <>
                      <Icon
                        name="notFound"
                        width="70%"
                        height="80%"
                        wrapperWidth="80%"
                        wrapperHeight="50%"
                        {...emptyIconProps}
                      />
                      <Typography margin="20px 0" textAlign="center" type="h2">
                        {emptyText}
                      </Typography>
                    </>
                  )
                }
                headerHeight={headerHeight}
                headerTypographyType={headerTypographyType}
                height={containerNotFound ? maxHeight : height}
                isAllSelected={isAllSelected}
                isSelectAllDisabled={isSelectAllDisabled}
                maxHeight={maxHeight}
                onColumnSort={onColumnSort}
                rowClassName={rowClassName}
                rowEventHandlers={{ onClick: onRowClick }}
                rowHeight={rowHeight}
                rowKey={rowKey || 'id'}
                selectable={selectable}
                sortBy={sortBy}
                width={width - paddingsAndBorders}
                {...rest}
              >
                {children}
              </StyledBaseTable>
            </StyledTableWrapper>
            {summaryData && !!data?.length && (
              <ErrorBoundary size="small">
                <TableSummary summaryData={summaryData} headerHeight={headerHeight} />
              </ErrorBoundary>
            )}
          </>
        )
      }}
    </AutoResizer>
  )
}

export default withTheme(Table)
