import React from 'react'
import ReactTexty from 'react-texty'

import { getAvailableTranslation } from '../../../utilsDataRelated/translations'

const TranslationsCell = ({ translations, defaultLanguage, currentLanguage }) => {
  const translatedText = getAvailableTranslation(translations, defaultLanguage, currentLanguage)

  return <ReactTexty>{translatedText}</ReactTexty>
}

export default TranslationsCell
