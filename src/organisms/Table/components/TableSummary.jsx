import React from 'react'
import { withTheme } from 'styled-components'

import { StyledSummaryCell, StyledTableSummary } from '../styled'

const TableSummary = ({ headerHeight, summaryData }) => {
  return (
    <StyledTableSummary headerHeight={headerHeight} className="summary">
      {summaryData.title && <div className="summaryTitle">{summaryData.title}</div>}
      {summaryData.data.map((item, i) => (
        <StyledSummaryCell
          className="summaryCell"
          key={i}
          width={item.width}
          flexGrow={item.flexGrow}
          padding={item.padding}
        >
          {item.label && <div className="summaryLabel">{item.label}:</div>}
          <div className="summaryValue">{item.value}</div>
        </StyledSummaryCell>
      ))}
    </StyledTableSummary>
  )
}

export default TableSummary
