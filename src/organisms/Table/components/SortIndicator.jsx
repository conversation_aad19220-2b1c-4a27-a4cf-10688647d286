import React from 'react'
import { withTheme } from 'styled-components'

import Icon from '../../../atoms/Icon'

const SortIndicator = ({ className, sortOrder, theme }) => {
  return (
    <>
      {sortOrder === 'asc' && (
        <Icon name="chevronUp" fill={theme.color.general.gray2} className={className} />
      )}
      {sortOrder === 'desc' && <Icon name="chevronDown" className={className} />}
    </>
  )
}

export default withTheme(SortIndicator)
