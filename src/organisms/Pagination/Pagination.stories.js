import React from 'react'
import { withKnobs, number, text } from '@storybook/addon-knobs'

import Pagination from './Pagination'

export default {
  title: 'Organisms/Pagination',
  component: Pagination,
}

export const pagination = () => (
  <Pagination
    itemsCount={number('Number of collection length', undefined)}
    itemsPerRowCount={number('Number of items per row', undefined)}
    rowsCount={number('Number of page rows', undefined)}
    currentPage={number('Number of current page', 1)}
    dashText={text('Text between page number and total number', 'of')}
  />
)

pagination.story = {
  decorators: [withKnobs],
}
