import React from 'react'
import { PropTypes as T } from 'prop-types'
import { withTheme } from 'styled-components'

import { StyledPaginationWrapper } from './styled'
import useWindowWidth from '../../hooks/useWindowWidth'
import Typography from '../../atoms/Typography'
import Icon from '../../atoms/Icon'

const Pagination = ({
  currentPage,
  itemsCount,
  itemsPerRowCount = 1,
  onPageChange,
  rowsCount,
  theme,
  dashText,
}) => {
  if (!itemsCount || !rowsCount) {
    return null
  }

  const width = useWindowWidth()
  const isMobile = width && width < theme.breakpoints.md

  const itemsPerPageCount = rowsCount * itemsPerRowCount
  const totalPageCount = Math.ceil(itemsCount / itemsPerPageCount)
  const indexOfLastRecord =
    currentPage * itemsPerPageCount > itemsCount ? itemsCount : currentPage * itemsPerPageCount
  const indexOfFirstRecord =
    indexOfLastRecord === itemsCount
      ? (totalPageCount - 1) * itemsPerPageCount + 1
      : indexOfLastRecord - itemsPerPageCount + 1

  const text = isMobile
    ? `${indexOfFirstRecord} - ${indexOfLastRecord}`
    : `${indexOfFirstRecord} - ${indexOfLastRecord} ${dashText} ${itemsCount}`

  const isDisabledPrev = currentPage === 1
  const isDisabledNext = currentPage === totalPageCount

  const onNext = () => {
    onPageChange(currentPage + 1)
  }

  const onPrevious = () => {
    onPageChange(currentPage - 1)
  }

  return (
    <StyledPaginationWrapper>
      <Typography type="body2" text={text} color={theme.color.general.gray3} />
      <Icon
        name="arrowLeft"
        stroke={isDisabledPrev ? theme.color.general.gray3 : ''}
        strokeWidth={1}
        disabled={totalPageCount === 1 || isDisabledPrev}
        className="previousPageButton"
        onClick={onPrevious}
      />
      <Icon
        name="arrowLeft"
        stroke={isDisabledNext ? theme.color.general.gray3 : ''}
        strokeWidth={1}
        disabled={totalPageCount === 1 || isDisabledNext}
        className="nextPageButton"
        onClick={onNext}
      />
    </StyledPaginationWrapper>
  )
}

export default withTheme(Pagination)

Pagination.propTypes = {
  itemsCount: T.number,
  itemsPerRowCount: T.number,
  rowsCount: T.number,
}
