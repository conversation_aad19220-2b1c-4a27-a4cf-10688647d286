import React from 'react'
import { object, select, withKnobs } from '@storybook/addon-knobs'
import Dropdown from './Dropdown'

export default {
  title: 'Molecules/Dropdown',
  component: Dropdown,
}

export const menu = () => {
  return (
    <Dropdown
      buttonProps={object('Button props', {})}
      openDirection={select('Dropdown align', ['left', 'right'], 'left')}
      dropdownItems={[
        { id: '1', label: 'One' },
        { id: '2', label: 'Two Two' },
        { id: '3', label: 'Three Three Three' },
        { id: '4', label: 'Four Four Four Four' },
      ]}
    />
  )
}

menu.story = {
  decorators: [withKnobs],
}
