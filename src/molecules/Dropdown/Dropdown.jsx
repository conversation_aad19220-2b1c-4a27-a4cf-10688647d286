'use client';
import { oneOf } from 'prop-types'
import React, { useState } from 'react'

import clsx from 'clsx'
import FlexRow from '../../atoms/FlexRow'
import HeaderMenuIcon from '../HeaderMenuIcon'
import { StyledDropdown, StyledMenu } from './styled'

const DropdownMenu = ({
  children,
  buttonProps,
  MenuButton: MenuButtonProp,
  openDirection = 'left',
  className,
  dropdownItems,
  onItemClick,
}) => {
  const [menuOpened, setMenuOpened] = useState(false)

  const MenuButton = MenuButtonProp || HeaderMenuIcon

  const onBtnClick = () => {
    setMenuOpened((prev) => !prev)
  }

  const onClick = (id) => () => {
    if (onItemClick) onItemClick(id)
  }

  return (
    <StyledDropdown className={clsx(className, openDirection)}>
      <MenuButton onClick={onBtnClick} opened={menuOpened} {...buttonProps} className="menuButton" />
      <StyledMenu className={clsx(menuOpened && 'opened')} onClick={() => setMenuOpened(false)}>
        {dropdownItems?.length &&
          dropdownItems.map((option, index) => {
            return (
              <FlexRow
                onClick={onClick(option.id || index)}
                key={index}
                justifyContent={openDirection === 'left' ? 'end' : 'start'}
              >
                {option.label || option}
              </FlexRow>
            )
          })}
        {children}
      </StyledMenu>
    </StyledDropdown>
  )
}

export default DropdownMenu

DropdownMenu.propTypes = {
  openDirection: oneOf(['left', 'right']),
}
