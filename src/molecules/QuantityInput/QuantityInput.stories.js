import React from 'react'
import { boolean, withKnobs, number } from '@storybook/addon-knobs'

import QuantityInput from './QuantityInput'

export default {
  title: 'Molecules/QuantityInput',
  component: QuantityInput,
}

export const quantityInput = () => (
  <QuantityInput
    disabled={boolean('Disabled', false)}
    withBorder={boolean('With border', false)}
    min={number('Min', 1)}
  />
)

quantityInput.story = {
  decorators: [withKnobs],
}
