'use client'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledWrapper = styled.div`
  display: inline-flex;
  border: ${({ withBorder, theme: { color } }) => (withBorder ? `1px solid ${color.general.gray2}` : 'none')};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: 0 10px;

  &.fullWidth {
    display: flex;
    flex-grow: 1;

    input {
      width: 100%;
      padding: 9px 16px;
      text-align: left;
      .withButtons {
        padding: 0 16px;
        text-align: center;
      }
    }
  }
  &.hasError {
    border: 1px solid ${({ theme }) => theme.color.status.error};
  }
  &.disabled {
    opacity: 0.8;
  }
`

export const StyledQuantityInput = styled.input`
  ${({ variant, theme }) => getTokens(`input-${variant}-black-large`, theme)};
  border: none;
  padding: 0 4px;
  width: 42px;
  outline: none;
  text-align: center;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;

  .disabled & {
    cursor: not-allowed;
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  -moz-appearance: textfield;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
    font-size: 16px !important;
  }
`
