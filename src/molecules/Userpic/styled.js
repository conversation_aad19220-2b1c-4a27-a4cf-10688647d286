'use client'
import styled from 'styled-components'

import Image from '../../atoms/Image'

export const StyledUserpic = styled(Image)`
  border-radius: 50%;
  width: fit-content;
  position: relative;
  ${({ borderColor }) => borderColor && `border: 1px solid ${borderColor};`}

  .onlineIcon {
    position: absolute;
    bottom: 0;
    right: 0;
  }
  img {
    border-radius: 50%;
    min-width: ${({ width }) => width || '36px'};
  }
`
