'use client';
import clsx from 'clsx'
import { PropTypes as T } from 'prop-types'
import React from 'react'
import { withTheme } from 'styled-components'

import Icon from '../../atoms/Icon'
import AvatarDefault from '../../molecules/AvatarDefault/AvatarDefault'
import emptyUserpic from '../../static/images/emptyUserpic.png'
import { StyledUserpic } from './styled'

const Userpic = ({
  alt = 'avatar',
  colorIdle,
  colorOffline,
  colorOnline,
  disableImagekit,
  height,
  src = '',
  onlineState,
  iconName,
  imagekitParams,
  imagekitUrl,
  width,
  className,
  iconWidth,
  iconHeight,
  theme,
  backgroundColor,
  color,
  fullName,
  borderColor,
  borderRadius,
  ...otherProps
}) => {
  const stateColor = {
    online: colorOnline || theme.color.status.success,
    offline: colorOffline || theme.color.status.warning,
    idle: colorIdle || theme.color.status.inactive,
  }

  const shouldDisplayAvatarDefault = !src

  if (shouldDisplayAvatarDefault) {
    return (
      <AvatarDefault
        width={width}
        height={height}
        backgroundColor={backgroundColor}
        color={color}
        fullName={fullName}
        borderColor={borderColor}
        borderRadius={borderRadius}
      />
    )
  }
  return (
    <StyledUserpic
      alt={fullName || alt}
      src={src || emptyUserpic}
      imagekitParams={imagekitParams}
      imagekitUrl={imagekitUrl}
      height={height}
      width={width}
      className={clsx(className, 'userpic')}
      borderColor={borderColor}
      {...otherProps}
    >
      {(onlineState || iconName) && (
        <Icon
          name={iconName || 'state'}
          className="onlineIcon"
          fill={stateColor[onlineState]}
          width={iconWidth}
          height={iconHeight}
        />
      )}
    </StyledUserpic>
  )
}

Userpic.propTypes = {
  className: T.string,
  colorIdle: T.string,
  colorOffline: T.string,
  colorOnline: T.string,
  height: T.string,
  src: T.string,
  state: T.string,
  iconName: T.string,
  width: T.string,
  onlineState: T.string,
}

export default withTheme(Userpic)
