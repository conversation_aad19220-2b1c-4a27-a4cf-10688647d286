import React from 'react'
import { withKnobs, text, select } from '@storybook/addon-knobs'

import Comment from './Comment'

export default {
  title: 'Molecules/Comment',
  component: Comment,
}

export const comment = () => (
  <Comment
    comment={text('Comment', '')}
    dateCreated={text('Date created', '')}
    dateDeleted={text('Date deleted', '')}
    name={text('Name', '')}
    imageData={text('Image data', '')}
    tagText={text('Tag text', '')}
    state={select('State', ['posted', 'deleted'], 'posted')}
  />
)

comment.story = {
  decorators: [withKnobs],
}
