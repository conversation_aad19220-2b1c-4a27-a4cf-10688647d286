'use client';
import React from 'react'
import { withTheme } from 'styled-components'

import { StyledComment, StyledHeader, StyledTextSection } from './styled'
import Image from '../../atoms/Image'
import Typography from '../../atoms/Typography'
import Tag from '../../atoms/Tag'
import Label from '../../atoms/Label'

const Comment = ({
  comment,
  dateCreated,
  dateDeleted,
  dateFormat = (date) => date,
  name,
  imageData,
  imageProps = {},
  state,
  theme,
  tagText,
  tagProps = {},
}) => {
  const image = imageData ? (
    <Image src={imageData} radius="50%" height={46} width={46} {...imageProps} />
  ) : null
  const tagWithText = tagText ? <Tag text={tagText} type="gray" fontSize={10} {...tagProps} /> : null

  return (
    <StyledComment className="comment">
      {image}
      <StyledTextSection className={state}>
        <StyledHeader>
          <Typography type="caption1" text={name} />
          {dateCreated && <span className="dateDot">•</span>}
          {dateCreated && (
            <Typography className="date" type="body1" color={theme.color.general.gray3}>
              {dateFormat(dateCreated)}
            </Typography>
          )}
          {tagWithText}
        </StyledHeader>
        <Typography type="body1" text={comment} className="commentText" />
        {state === 'deleted' && (
          <Label className="deletedDate" color={theme.color.general.gray4}>
            Deleted on {dateFormat(dateDeleted)}
          </Label>
        )}
      </StyledTextSection>
    </StyledComment>
  )
}

export default withTheme(Comment)
