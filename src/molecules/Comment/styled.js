'use client'
import styled from 'styled-components'

export const StyledComment = styled.div`
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
`

export const StyledTextSection = styled.div`
  flex: 1;

  &.deleted {
    .commentText {
      text-decoration: line-through;
      color: ${({ theme }) => theme.color.general.gray4};
      overflow-wrap: anywhere;
    }

    .deletedDate {
      display: block;
      text-align: right;
    }
  }
`

export const StyledHeader = styled.div`
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;

  .tag {
    margin-left: 7px;
  }

  .dateDot {
    margin: 0 4px;
    color: ${({ theme }) => theme.color.general.gray3};
  }
`
