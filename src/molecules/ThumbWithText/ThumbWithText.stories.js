import { text, withKnobs } from '@storybook/addon-knobs'
import React from 'react'

import ThumbWithText from './ThumbWithText'

export default {
  title: 'Molecules/ThumbWithText',
  component: ThumbWithText,
}

export const thumbWithText = () => (
  <ThumbWithText src={text('src', '')} text={text('text', 'Text')} subText={text('subText', 'Sub text')} />
)

thumbWithText.story = {
  decorators: [withKnobs],
}
