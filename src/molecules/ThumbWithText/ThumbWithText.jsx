import React from 'react'
import ReactTexty from 'react-texty'
import { withTheme } from 'styled-components'

import Typography from '../../atoms/Typography'
import { StyledImage } from './styled'

const ThumbWithText = ({
  customTooltip,
  imageProps,
  src,
  text,
  textType = 'body2',
  textProps,
  subText,
  subTextProps,
  customSubTooltip,
  theme: { size },
}) => {
  return (
    <>
      {src && <StyledImage src={src} height={36} radius={size.border.radius.main} {...imageProps} />}
      <div style={{ overflow: 'hidden' }}>
        <ReactTexty tooltip={customTooltip}>
          <Typography text={text} type={textType} {...textProps} />
        </ReactTexty>
        {subText && (
          <ReactTexty tooltip={customSubTooltip}>
            <Typography text={subText} {...subTextProps} />
          </ReactTexty>
        )}
      </div>
    </>
  )
}

export default withTheme(ThumbWithText)
