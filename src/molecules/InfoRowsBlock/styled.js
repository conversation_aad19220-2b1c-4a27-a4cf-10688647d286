'use client'
import styled from 'styled-components'
import FlexRow from '../../atoms/FlexRow'

export const StyledInfoRowsBlock = styled.div`
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: ${({ theme, borderRadius }) => borderRadius || theme.size.border.radius.main};

  .dataRow {
    border-top: 1px solid ${({ theme }) => theme.color.general.gray2};
    &:first-child {
      border-top: none;
    }
    &:last-child {
      border-bottom: none;
    }
  }

  .spaced {
    letter-spacing: 2px;
  }
`

export const StyledDataItem = styled(FlexRow)`
  width: ${({ width }) => width || '100%'};
  border-right: ${({ theme, hideBorder }) =>
    hideBorder ? 'none' : `1px solid ${theme.color.general.gray2}`};
  &:last-child {
    border-right: none;
  }
`
