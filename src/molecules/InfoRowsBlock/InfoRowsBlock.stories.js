import { object, text, withKnobs } from '@storybook/addon-knobs'
import React from 'react'

import InfoRowsBlock from './InfoRowsBlock'

export default {
  title: 'Molecules/InfoRowsBlock',
  component: InfoRowsBlock,
}

export const infoRowsBlock = () => (
  <InfoRowsBlock
    title={text('title', 'Title')}
    data={object('My Object', [
      {
        createdAt: {
          iconProps: { name: 'calendarAdd' },
          text: 'Formated date',
          width: '33.33%',
        },
        name: {
          text: '<PERSON>lqewh<PERSON>kchlhcvlqewhclk Doelkchlhcvlqewhclk',
          width: '33.33%',
        },
        dddd: {
          text: '<PERSON>',
          width: '33.33%',
        },
      },
      {
        surname: {
          text: '<PERSON><PERSON>',
          width: '100%',
        },
      },
    ])}
  />
)

infoRowsBlock.story = {
  decorators: [withKnobs],
}
