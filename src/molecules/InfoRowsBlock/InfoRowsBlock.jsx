import React from 'react'
import ReactTexty from 'react-texty'
import { withTheme } from 'styled-components'

import FlexRow from '../../atoms/FlexRow'
import Icon from '../../atoms/Icon'
import Typography from '../../atoms/Typography'
import { isObjectEmpty } from '../../utils'
import { StyledDataItem, StyledInfoRowsBlock } from './styled'
// data is array of objects, where each object is a row (of objects that are data items)

const InfoRowsBlock = ({ borderRadius, className, title, data = [{}], rowFlexProps = {}, theme }) => {
  return (
    <StyledInfoRowsBlock className={className} borderRadius={borderRadius}>
      {title && (
        <FlexRow className="titleRow" padding="10px">
          <Typography text={title} type="h4" className="spaced" as={ReactTexty} />
        </FlexRow>
      )}
      {data.map((rowObject) => (
        <FlexRow className="dataRow" flexWrap="wrap" {...rowFlexProps}>
          {!isObjectEmpty(rowObject) &&
            Object.keys(rowObject).map((row, i) => {
              const value = rowObject[row]

              if (!value || isObjectEmpty(value)) {
                return null
              }

              return (
                <StyledDataItem
                  className="dataItem"
                  padding={value.padding || '10px'}
                  key={i}
                  hideBorder={value.hideBorder}
                  width={value.width}
                  gap="5px"
                  {...(value.rowItemFlexProps || {})}
                >
                  {value.iconProps && (
                    <Icon
                      width={20}
                      height={20}
                      strokeWidth={1}
                      stroke={theme.color.general.gray5}
                      {...value.iconProps}
                    />
                  )}
                  {value.text && (
                    <Typography type="body1" text={value.text} as={ReactTexty} {...(value.textProps || {})} />
                  )}
                  {value.customElement}
                </StyledDataItem>
              )
            })}
        </FlexRow>
      ))}
    </StyledInfoRowsBlock>
  )
}

export default withTheme(InfoRowsBlock)
