'use client';
import React from 'react'
import { PropTypes as T } from 'prop-types'
import { withTheme } from 'styled-components'

import { StyledRating } from './styled'
import Icon from '../../atoms/Icon'
import Typography from '../../atoms/Typography'

const RatingView = (props) => {
  const { value, fill, rowData, theme, cellData } = props

  return (
    <StyledRating>
      <Icon
        name="starSign"
        fill={(value || cellData) === 0 ? theme.color.general.gray1 : fill}
        stroke={(value || cellData) === 0 ? theme.color.general.gray2 : theme.color.primary.gray2}
      />
      <Typography variant="button2" text={value || cellData} margin="0 0 0 8px" />
    </StyledRating>
  )
}

export default withTheme(RatingView)

RatingView.propTypes = {
  fill: T.string,
  value: T.oneOfType([T.string, T.number]),
  rowData: T.object,
  cellData: T.oneOfType([T.string, T.number]),
}
