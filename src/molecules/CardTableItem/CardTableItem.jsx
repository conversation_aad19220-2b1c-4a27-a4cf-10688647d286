import { pick } from 'dot-object'
import React from 'react'
import { withTheme } from 'styled-components'
import clsx from 'clsx'

import SelectionCell from '../../organisms/Table/components/SelectionCell'
import { StyledTableCard } from './styled'

const getValue = (columns, column, data = {}, cellProps, field) => {
  const value = pick(column?.dataKey || field || '', data)
  const res = column?.cellRenderer
    ? column.cellRenderer({ cellData: value, column, rowData: data, ...cellProps })
    : value

  return res
}

const CardTableItem = ({
  CardContent,
  cellProps,
  editMode,
  index,
  isClicked,
  onClick,
  onItemSelect,
  style,
  data,
  columns,
  margin,
  rowHeight,
  rowIndex,
  rowKey,
  t,
  theme,
  type,
  selectedRowKeys,
  selectable,
}) => {
  const getDataForRender = () => {
    if (!columns) {
      return data
    }

    if (selectable) {
      const selectionColumn = {
        key: 'checkbox',
        dataKey: 'checkbox',
        width: 25,
        rowKey: row<PERSON>ey || 'id',
        selectedRowKeys,
        cellRenderer: (props) => (
          <SelectionCell {...props} rowData={data} container={cellProps.container} rowIndex={rowIndex} />
        ),
        onChange: onItemSelect,
      }
      columns = [...columns, selectionColumn]
    }

    return Object.keys(data || {}).reduce((acc, field) => {
      const column = columns.find((el) => {
        const currKey = el.dataKey.includes('.') ? el.dataKey.split('.')[0] : el.dataKey
        if (currKey === 'checkbox') {
          field = currKey
        }
        return currKey === field
      })

      return {
        ...acc,
        [field]: getValue(columns, column, data, cellProps, field),
      }
    }, {})
  }

  return (
    <StyledTableCard
      margin={margin}
      rowHeight={rowHeight}
      style={style}
      className="tableCardWrapper"
      onClick={onClick}
    >
      <CardContent
        cellProps={cellProps}
        className={clsx(isClicked && 'clicked')}
        editMode={editMode}
        index={index}
        data={getDataForRender()}
        initialData={data}
        columns={columns}
        t={t}
        theme={theme}
        type={type}
      />
    </StyledTableCard>
  )
}

export default withTheme(CardTableItem)
