import styled from 'styled-components'

export const StyledTableCard = styled.div`
  height: ${({ rowHeight }) => `${rowHeight}px` || '100%'};
  margin: ${({ margin }) => `${margin / 2}px 10px` || '10px'};
  display: block;

  &:first-child {
    margin-top: 0;
  }

  .clicked {
    border-color: ${({ theme: { color } }) => color.primary.lighter};
    border-width: 2px;
  }

  // .inactive {
  //   color: ${({ theme: { color } }) => color.general.light};
  //
  //   .icon:not(~ .statusIcon) {
  //     path {
  //       fill: ${({ theme: { color } }) => color.general.lightest};
  //       stroke: ${({ theme: { color } }) => color.general.lighter};
  //     }
  //     circle {
  //       fill: ${({ theme: { color } }) => color.status.inactive};
  //     }
  //   }
  //   .userpic {
  //     opacity: 0.2;
  //   }
  //   .typography {
  //     color: ${({ theme: { color } }) => color.general.light};
  //   }
  //   .tag {
  //     background-color: ${({ theme: { color } }) => color.general.lighter};
  //     color: ${({ theme: { color } }) => color.general.darker};
  //   }
`
