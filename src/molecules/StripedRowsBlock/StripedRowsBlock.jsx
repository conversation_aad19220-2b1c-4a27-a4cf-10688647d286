import React from 'react'

import Typography from '../../atoms/Typography'
import { StyledTable } from './styled'

const StripedRowsBlock = ({ data }) => {
  return (
    <StyledTable>
      {Object.entries(data).map((row) => {
        if (!row[1]) {
          return null
        }

        return (
          <tbody key={row[0]}>
            <tr>
              <td className="nameCell">
                <Typography variant="caption1">{row[0]}</Typography>
              </td>
              <td className="valueCell">
                <Typography variant="body2">{row[1]}</Typography>
              </td>
            </tr>
          </tbody>
        )
      })}
    </StyledTable>
  )
}

export default StripedRowsBlock
