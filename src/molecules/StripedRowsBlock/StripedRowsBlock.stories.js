import { object, withKnobs } from '@storybook/addon-knobs'
import React from 'react'

import StripedRowsBlock from './StripedRowsBlock'

export default {
  title: 'Molecules/StripedRowsBlock',
  component: StripedRowsBlock,
}
export const stripedRowsBlock = () => (
  <StripedRowsBlock
    data={object('data', {
      value1: 'Value 1',
      value2: 'Value 2',
      value3: 'Value 3',
    })}
  />
)

stripedRowsBlock.story = {
  decorators: [withKnobs],
}
