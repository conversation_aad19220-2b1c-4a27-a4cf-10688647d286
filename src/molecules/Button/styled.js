'use client'
import styled from 'styled-components'

import getTokens from '../../utils/getTokens'

export const StyledButton = styled.button`
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  white-space: nowrap;

  &.uppercase {
    text-transform: uppercase;
  }
  &.withLetterSpacing {
    letter-spacing: 0.05em;
  }

  &.withIcon {
    min-width: inherit;
    justify-content: space-between;
    text-align: left;
  }

  &.primary {
    border: 1px solid transparent;
    &:hover:not(.disabled) {
      background-color: ${({ theme, $hoverType }) => theme.color.primary[$hoverType]};
    }
    &.withTextShadow {
      text-shadow: 0 2px 5px ${({ theme }) => theme.color.primary.darker};
    }
  }
  &.secondary {
    border: 1px solid transparent;
    &:hover:not(.disabled) {
      background-color: ${({ theme, $hoverType }) => theme.color.secondary[$hoverType]};
    }
    &.withTextShadow {
      text-shadow: 0 2px 5px ${({ theme }) => theme.color.secondary.darker};
    }
  }
  &.bordered {
    border: ${({ theme }) => `1px solid ${theme.color.general.gray2}`};
    &:hover:not(.disabled) {
      border: ${({ theme }) => `1px solid ${theme.color.general.gray3}`};
    }
  }

  &.disabled {
    opacity: ${({ $disableType }) => ($disableType === 'opacity' ? 0.5 : 1)};
    cursor: not-allowed;

    &.withTextShadow {
      text-shadow: none;
    }
  }

  &.primary,
  &.secondary,
  &.bordered {
    padding: '8px 17px';
    ${({ variant, size, theme }) => getTokens(`button-standard-${variant}-${size}`, theme)};
  }

  &.disabled {
    ${({ $disableType, size, theme }) =>
      $disableType === 'color' ? getTokens(`button-standard-disabled-${size}`, theme) : undefined};
  }

  &.primary,
  &.secondary,
  &.bordered,
  &.disabled {
    border-radius: ${({ borderRadius }) => borderRadius};
    ${({ backgroundColor }) => backgroundColor && `background-color: ${backgroundColor}`};
    ${({ color }) => color && `color: ${color}`};
    padding: ${({ padding }) => padding};
  }

  &.fullWidth {
    width: 100%;
  }

  &.googleButton {
    background-color: ${({ theme }) => theme.color.general.light};
    text-shadow: none;

    &.disabled {
      svg {
        opacity: 0.4;
      }
    }
  }

  &.fbButton {
    background-color: #1877f2;
    &:hover:not(.disabled) {
      background-color: #1877f2;
      opacity: 0.8;
    }
    &.disabled {
      svg {
        opacity: 0.6;
      }
    }
  }

  &.twitterButton {
    background-color: #1da1f2;
    &:hover:not(.disabled) {
      background-color: #1da1f2;
      opacity: 0.8;
    }
    &.disabled {
      svg {
        opacity: 0.6;
      }
    }
  }
`
