'use client';
import clsx from 'clsx'
import { PropTypes as T } from 'prop-types'
import React from 'react'
import { withTheme } from 'styled-components'

import { useEffect } from 'react'
import Icon from '../../atoms/Icon'
import { useTimeout } from '../../hooks/useTimeout'
import Tooltip from '../Tooltip'
import { StyledButton } from './styled'

const Button = (props) => {
  const {
    borderRadius,
    children,
    className,
    color,
    fullWidth,
    disabled,
    disableType = 'opacity',
    hoverType = 'dark',
    iconName,
    iconSize,
    iconRightProps,
    iconLeftProps,
    onClick,
    padding,
    size = 'large',
    text,
    tooltipProps,
    theme,
    uppercase = true,
    variant = 'primary',
    withIcon,
    withLetterSpacing = true,
    withTextShadow,
    confirmButtonProps,
    t,
    ...otherProps
  } = props

  const [isConfirmDeleteActive, setIsConfirmDeleteActive] = React.useState(false)
  const deleteConfirmRef = React.useRef(null)

  const onDeleteClick = (e) => {
    e.preventDefault()
    setIsConfirmDeleteActive(true)
  }

  useTimeout(
    () => {
      setIsConfirmDeleteActive(false)
    },
    3000,
    [isConfirmDeleteActive]
  )

  const onDeleteClickConfirm = (e) => {
    e.preventDefault()
    if (confirmButtonProps.onDeleteConfirm) {
      confirmButtonProps.onDeleteConfirm(e)
    }

    if (onClick) {
      onClick(e)
    }

    deleteConfirmRef.current = setTimeout(() => {
      setIsConfirmDeleteActive(false)
    }, 1000)
  }

  useEffect(() => {
    return () => {
      clearTimeout(deleteConfirmRef.current)
    }
  }, [])

  const confirmButtonPropsExtended =
    confirmButtonProps && isConfirmDeleteActive
      ? {
          backgroundColor: confirmButtonProps.backgroundColor || theme.color.general.light,
          color: confirmButtonProps.color || theme.color.status.error,
          variant: confirmButtonProps.variant || 'primary',
          onClick: onDeleteClickConfirm,
        }
      : {}

  const confirmIconProps = confirmButtonProps && isConfirmDeleteActive && confirmButtonProps.iconLeftProps
  const confirmText = confirmButtonProps && isConfirmDeleteActive && t('clickToConfirm')
  const hasChildren = !!children || !!confirmText || !!text

  return (
    <Tooltip {...tooltipProps} disableTooltip={tooltipProps ? tooltipProps.disableTooltip : true}>
      <StyledButton
        $borderRadius={borderRadius}
        $disableType={disableType}
        disabled={disabled}
        $hoverType={hoverType}
        onClick={confirmButtonProps ? onDeleteClick : onClick}
        padding={padding}
        size={size}
        variant={variant}
        theme={theme}
        className={clsx(
          className,
          variant,
          disabled && 'disabled',
          fullWidth && 'fullWidth',
          (withIcon || iconName || iconLeftProps || iconRightProps) && 'withIcon',
          withTextShadow && 'withTextShadow',
          uppercase && 'uppercase',
          withLetterSpacing && 'withLetterSpacing',
          confirmButtonProps && isConfirmDeleteActive && 'confirmState',
          confirmButtonProps && isConfirmDeleteActive && confirmButtonProps?.classNameConfirm
        )}
        color={color}
        {...otherProps}
        {...confirmButtonPropsExtended}
      >
        {(iconName || iconLeftProps) && (
          <Icon
            name={iconName}
            fill={
              disabled ? theme.components.button.disabled?.large?.color : color || theme.color.general.white
            }
            margin={hasChildren ? '0 6px 0 0' : '0'}
            size={iconSize || 16}
            {...iconLeftProps}
            {...confirmIconProps}
          />
        )}
        {children || confirmText || text}
        {iconRightProps && (
          <Icon
            fill={
              disabled ? theme.components.button.disabled?.large?.color : color || theme.color.general.white
            }
            margin="0 0 0 6px"
            {...iconRightProps}
          />
        )}
      </StyledButton>
    </Tooltip>
  )
}

export default withTheme(Button)

Button.propTypes = {
  borderRadius: T.string,
  children: T.node,
  className: T.string,
  color: T.string,
  disabled: T.bool,
  disableType: T.oneOf(['opacity', 'color']),
  fullWidth: T.bool,
  hoverType: T.oneOf(['lighter', 'dark']),
  iconName: T.string,
  iconRightProps: T.object,
  iconLeftProps: T.object,
  onClick: T.func,
  padding: T.string,
  size: T.oneOf(['small', 'medium', 'large']),
  text: T.string,
  tooltipProps: T.object,
  uppercase: T.bool,
  variant: T.oneOf(['primary', 'secondary', 'success', 'bordered']).isRequired,
  withIcon: T.bool,
  withLetterSpacing: T.bool,
  withTextShadow: T.bool,
}
