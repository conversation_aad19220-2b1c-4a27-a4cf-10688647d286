import { boolean, color, select, withKnobs } from '@storybook/addon-knobs'
import React from 'react'

import Button from './Button'

export default {
  title: 'Molecules/Button',
  component: Button,
}

export const primary = () => (
  <Button
    variant="primary"
    disabled={boolean('Disabled', false)}
    size={select('Size', ['small', 'medium', 'large'], 'large')}
    color={color('Color', '')}
    backgroundColor={color('BG Color', '')}
  >
    Primary
  </Button>
)

export const secondary = () => (
  <Button
    variant="secondary"
    disabled={boolean('Disabled', false)}
    size={select('Size', ['small', 'medium', 'large'], 'medium')}
    color={color('Color', '')}
    backgroundColor={color('BG Color', '')}
  >
    Secondary
  </Button>
)

export const bordered = () => (
  <Button
    variant="bordered"
    disabled={boolean('Disabled', false)}
    size={select('Size', ['small', 'medium', 'large'], 'medium')}
    color={color('Color', '')}
    backgroundColor={color('BG Color', '')}
  >
    Secondary
  </Button>
)

primary.story = {
  decorators: [withKnobs],
}

secondary.story = {
  decorators: [withKnobs],
}
