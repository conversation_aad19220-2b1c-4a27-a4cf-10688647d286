'use client';
import React, { useEffect, useState } from 'react'
import { PropTypes as T } from 'prop-types'
import { withTheme } from 'styled-components'
import clsx from 'clsx'

import Icon from '../../atoms/Icon'
import { StyledCheckboxWrapper } from './styled'
import Typography from '../../atoms/Typography'

const Checkbox = ({
  checked,
  className,
  disabled,
  fontWeight,
  handleChange,
  iconVariant = 'checkbox',
  label,
  labelProps,
  labelType,
  name,
  readOnly,
  theme: { color },
  type = 'checkbox',
  value,
  variant = 'secondary',
  ...otherProps
}) => {
  const onChange = (e) => {
    handleChange && handleChange(e.target.checked, e)
  }

  const onLabelClick = () => {
    handleChange && handleChange(type === 'radio' ? true : !checked, { target: { name, value } })
  }

  return (
    <StyledCheckboxWrapper className={clsx(className, disabled && 'disabled', type)}>
      <input
        type={type}
        checked={checked}
        disabled={disabled}
        name={name}
        onChange={disabled || readOnly ? undefined : onChange}
        readOnly={readOnly}
        value={value}
        {...otherProps}
      />
      {checked ? (
        <Icon name={`${iconVariant}Checked`} fill={color[variant].main} />
      ) : (
        <Icon name={`${iconVariant}Unchecked`} />
      )}
      {label && (
        <Typography
          type={labelType || 'body1'}
          onClick={!disabled && !readOnly && onLabelClick}
          fontWeight={fontWeight}
          {...labelProps}
        >
          {label}
        </Typography>
      )}
    </StyledCheckboxWrapper>
  )
}

export default withTheme(Checkbox)

Checkbox.propTypes = {
  type: T.string.isRequired,
  variant: T.oneOf(['primary', 'secondary']),
  iconVariant: T.oneOf(['checkbox', 'checkboxSquared']),
}
