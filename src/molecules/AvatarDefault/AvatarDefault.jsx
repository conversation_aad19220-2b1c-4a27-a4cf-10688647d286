import { PropTypes as T } from 'prop-types'
import React from 'react'
import { withTheme } from 'styled-components'

import { StyledAvatarCircle } from './styled'

const AvatarDefault = ({
  backgroundColor,
  color,
  height = '76px',
  fullName = '',
  textTransform = 'uppercase',
  width = '76px',
  borderColor,
  borderRadius,
}) => {
  if (!fullName) {
    return null
  }

  const getInitials = () => {
    return fullName
      .split(' ')
      .slice(0, 2)
      .map((n) => n.charAt(0))
      .join('')
  }

  return (
    <StyledAvatarCircle
      width={width}
      height={height}
      backgroundColor={backgroundColor}
      color={color}
      textTransform={textTransform}
      borderColor={borderColor}
      borderRadius={borderRadius}
    >
      {getInitials()}
    </StyledAvatarCircle>
  )
}
export default withTheme(AvatarDefault)

AvatarDefault.propTypes = {
  backgroundColor: T.string,
  color: T.string,
  textTransform: T.string,
  width: T.string,
  height: T.string,
  fullName: T.string,
}
