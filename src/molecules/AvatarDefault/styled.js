'use client'
import styled from 'styled-components'

export const StyledAvatarCircle = styled.div`
  flex-shrink: 0;
  width: ${({ width }) => width};
  height: ${({ height }) => height};
  background-color: ${({ backgroundColor, theme }) => backgroundColor || theme.color.general.dark};
  color: ${({ color, theme }) => color || theme.color.general.light};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: calc(${({ height }) => height} / 2);
  font-weight: 700;
  text-transform: ${({ textTransform }) => textTransform};
  ${({ borderColor }) => borderColor && `border: 1px solid ${borderColor};`}
  ${({ borderRadius }) => borderRadius && `border-radius: ${borderRadius};`}
`
