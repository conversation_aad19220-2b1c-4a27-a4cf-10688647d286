import { color, text, withKnobs } from '@storybook/addon-knobs'
import React from 'react'

import AvatarDefault from './AvatarDefault'

export default {
  title: 'Molecules/AvatarDefault',
  component: AvatarDefault,
}

export const customAvatar = () => (
  <AvatarDefault
    width={text('Width', '78px')}
    height={text('Height', '78px')}
    backgroundColor={color('Background color', '')}
    color={color('Color', '')}
    fullName={text('Full name', '<PERSON>')}
    borderColor={color('Border color', '')}
  />
)

customAvatar.story = {
  decorators: [withKnobs],
}
