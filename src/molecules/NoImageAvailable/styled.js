import styled from 'styled-components'

export const StyledNoImageAvailable = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  height: ${({ height }) => (height ? `${height}${typeof height === 'number' ? 'px' : ''}` : 'auto')};
  width: ${({ width }) => (width ? `${width}${typeof width === 'number' ? 'px' : ''}` : 'auto')};
  max-height: 100%;
  margin: ${({ margin }) => margin};
  background-color: ${({ theme }) => theme.color.general.gray1};
  border-radius: ${({ radius }) => `${radius || 0}${typeof radius === 'number' ? 'px' : ''}`};
`
