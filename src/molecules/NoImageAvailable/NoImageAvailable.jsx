import React from 'react'
import { withTheme } from 'styled-components'
import { PropTypes as T } from 'prop-types'

import Icon from '../../atoms/Icon'
import { StyledNoImageAvailable } from './styled'

const NoImageAvailable = ({
  height,
  width,
  margin,
  radius,
  className,
  iconWidth,
  iconHeight,
  ...otherProps
}) => {
  return (
    <StyledNoImageAvailable
      height={height}
      width={width}
      className={className}
      margin={margin}
      radius={radius}
      {...otherProps}
    >
      <Icon name="noImage" wrapperWidth={iconWidth} wrapperHeight={iconHeight} />
    </StyledNoImageAvailable>
  )
}

export default withTheme(NoImageAvailable)

NoImageAvailable.propTypes = {
  height: T.string,
  width: T.string,
  margin: T.string,
  radius: T.string,
  className: T.string,
  iconWidth: T.string,
  iconHeight: T.string,
}
