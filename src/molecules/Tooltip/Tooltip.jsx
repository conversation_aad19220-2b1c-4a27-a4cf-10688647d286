'use client';
import React from 'react'
import { PropTypes as T } from 'prop-types'
import clsx from 'clsx'

import { StyledTooltip } from './styled'
import Typography from '../../atoms/Typography'

const Tooltip = ({
  borderRadius,
  children,
  className,
  disableTooltip,
  left,
  right,
  padding,
  text,
  textVariant,
  color,
  textColor,
  tipPosition = 'bottom',
  arrowPosition = 'center',
  whiteSpace,
  width,
  ...rest
}) => {
  if (disableTooltip) {
    return children
  }

  const [show, setShow] = React.useState(false)

  return (
    <StyledTooltip
      color={color}
      textColor={textColor}
      className={className}
      left={left}
      right={right}
      whiteSpace={whiteSpace}
      width={width}
      padding={padding}
      borderRadius={borderRadius}
    >
      <div
        className={clsx(arrowPosition, tipPosition, 'tooltip')}
        style={show ? { visibility: 'visible' } : {}}
      >
        <Typography text={text} type={textVariant || 'body2'} className="tooltipText" />
        <span className={clsx(arrowPosition, 'tooltip-arrow')} />
      </div>
      <div {...rest} onMouseEnter={() => setShow(true)} onMouseLeave={() => setShow(false)}>
        {children}
      </div>
    </StyledTooltip>
  )
}

export default Tooltip

Tooltip.propTypes = {
  children: T.node,
  className: T.string,
  color: T.string,
  left: T.string,
  right: T.string,
  textColor: T.string,
  text: T.string,
  tipPosition: T.oneOf(['top', 'bottom']),
  arrowPosition: T.oneOf(['left', 'center', 'right']),
  whiteSpace: T.string,
}
