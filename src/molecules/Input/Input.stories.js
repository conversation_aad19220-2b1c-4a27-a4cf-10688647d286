import { boolean, select, text, withKnobs } from '@storybook/addon-knobs'
import React from 'react'
import Input from './Input'

export default {
  title: 'Molecules/Input',
  component: Input,
}

export const input = () => (
  <div style={{ backgroundColor: `${boolean('Inverted', false) ? 'black' : 'white'}` }}>
    <Input
      variant={select('Input type', ['primary', 'secondary', 'transparent'], 'primary')}
      size={select('Input size', ['small', 'medium', 'large'], 'large')}
      label={text('Label', '')}
      labelType={select('Label type', ['top', 'left'], 'top')}
      withBorder={boolean('With border', false)}
      withFocusBorder={boolean('With focus border', false)}
      withFocusOutline={boolean('With focus outline', false)}
      disabled={boolean('Disabled', false)}
      required={boolean('Required', false)}
      inverted={boolean('Inverted', false)}
      multiline={boolean('Multiline', false)}
      boldLabel={boolean('Bold label', false)}
      withAutosize={boolean('With autosize', false)}
      placeholder="Enter your name"
      error={text('Error Label', '')}
      rightIconName={text('Right icon name', '')}
      leftIconName={text('Left icon name', '')}
      type={select('Type of input', ['text', 'password', 'number', 'richText', 'time'], 'text')}
      focusBorderColorKey={text('Focus border color key', 'primary.main')}
      focusOutlineColorKey={text('Focus outline color key', 'primary.lighter')}
    />
  </div>
)

input.story = {
  decorators: [withKnobs],
}
