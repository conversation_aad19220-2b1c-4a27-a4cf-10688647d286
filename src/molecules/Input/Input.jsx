'use client';
import clsx from 'clsx'
import { PropTypes as T } from 'prop-types'
import React, { forwardRef, useEffect } from 'react'

import Icon from '../../atoms/Icon'
import useAutosizeTextArea from '../../hooks/useAutosizeTextArea'
import useDebounce from '../../hooks/useDebounce'
import RichTextEditor from '../../organisms/RichTextEditor'
import { isObjectEmpty } from '../../utils'
import QuantityInput from '../QuantityInput'
import {
  StyledErrorLabel,
  StyledFlexRow,
  StyledInput,
  StyledInputWrapper,
  StyledLabel,
  StyledWrapper,
} from './styled'
import { isClient } from '../../utils/isClient'

const Input = forwardRef((props, ref) => {
  const {
    boldLabel,
    className,
    disabled,
    error,
    focusBorderColorKey = 'primary.main',
    focusOutlineColorKey = 'primary.lighter',
    fullWidth,
    iconLeftProps,
    iconRightProps,
    initialHeight,
    initialValue,
    label,
    labelType = 'top',
    labelWidth,
    maxHeight,
    multiline,
    name,
    onChange,
    onEnter,
    onLeftIconClick,
    onRightIconClick,
    onSubmit,
    placeholder,
    placeholderColor,
    size = 'large',
    submitByEnterPressed = true,
    success,
    type,
    required,
    rightIconName,
    leftIconName,
    value,
    variant = 'primary',
    withAutosize,
    withBorder,
    withDebounce,
    withFocusBorder,
    withFocusOutline,
    withoutValidation,
    ...otherProps
  } = props
  const debouncedValue = useDebounce(value, 300)

  useAutosizeTextArea(withAutosize, ref?.current || ref, value, maxHeight, initialHeight)

  const onChangeState = (e) => {
    if (onChange && !withDebounce) {
      onChange(e)
    }
  }

  useEffect(() => {
    if (onChange && withDebounce) {
      onChange(debouncedValue)
    }
  }, [withDebounce, debouncedValue, onChange])

  const handleKeyDown = (e) => {
    // it triggers by pressing the enter key
    if (!disabled && submitByEnterPressed && (onEnter || onSubmit) && e.key === 'Enter') {
      if (onSubmit) {
        onSubmit(e.target.value)
      }
      if (onEnter) {
        onEnter(e.target.value)
      }
    }
  }

  const hasLeftIcon = !isObjectEmpty(iconLeftProps)
  const hasRightIcon = !isObjectEmpty(iconRightProps)

  const getInputWithoutLeftLabel = () => {
    const classes = clsx(
      !!error && 'hasError',
      success && 'success',
      !!(leftIconName || hasLeftIcon) && 'hasLeftIcon',
      !!(rightIconName || hasRightIcon || error) && 'hasRightIcon',
      multiline && 'multiline',
      withBorder && 'withBorder',
      withFocusBorder && 'withFocusBorder',
      withFocusOutline && 'withFocusOutline'
    )

    let InputField

    if (type === 'number') {
      InputField = <QuantityInput {...props} className={classes} />
    } else if (type === 'richText' && isClient) {
      InputField = (
        <RichTextEditor
          inputClassName={classes}
          readOnly={disabled}
          toolbarOptions={props.toolbarOptions}
          {...props}
        />
      )
    } else {
      InputField = (
        <StyledInput
          onKeyDown={handleKeyDown}
          as={multiline && 'textarea'}
          ref={ref}
          name={name}
          disabled={disabled}
          focusBorderColorKey={focusBorderColorKey}
          focusOutlineColorKey={focusOutlineColorKey}
          value={value}
          onChange={onChangeState}
          placeholder={placeholder}
          placeholderColor={placeholderColor}
          size={size}
          variant={variant}
          type={type}
          className={classes}
          {...otherProps}
        />
      )
    }

    return (
      <StyledInputWrapper
        className={clsx(
          className,
          withoutValidation && 'withoutValidation',
          error && 'hasError',
          'inputContainer'
        )}
        fullWidth={fullWidth}
      >
        {label && labelType === 'top' && (
          <StyledLabel className={clsx('label', variant, boldLabel && 'boldLabel', required && 'required')}>
            {label}
          </StyledLabel>
        )}
        <StyledWrapper
          variant={variant}
          fullWidth={fullWidth}
          className={clsx(
            'inputWrapper',
            variant,
            disabled && 'disabled',
            !!error && 'hasError',
            success && 'success',
            labelType === 'top' && className,
            withoutValidation && 'withoutValidation'
          )}
        >
          {!!(hasLeftIcon || leftIconName) && (
            <Icon
              width={16}
              height={16}
              className="leftIcon"
              name={leftIconName}
              onClick={onLeftIconClick}
              {...iconLeftProps}
            />
          )}

          {InputField}

          {(error || rightIconName || hasRightIcon) && (
            <Icon
              name={rightIconName || 'error'}
              size={14}
              className="rightIcon"
              onClick={onRightIconClick}
              {...iconRightProps}
            />
          )}

          {success && (
            <Icon
              name="checkRounded"
              borderRadius="50%"
              wrapperHeight={14}
              wrapperWidth={14}
              width={8}
              className="rightIcon success"
            />
          )}
        </StyledWrapper>
        {!!error && typeof error === 'string' && (
          <StyledErrorLabel icon={<Icon name="inputError" size={13} />} className="error">
            {error}
          </StyledErrorLabel>
        )}
      </StyledInputWrapper>
    )
  }

  return label && labelType === 'left' ? (
    <StyledFlexRow
      className={clsx(
        'labelLeft',
        variant,
        'leftLabelWrapper',
        className,
        withoutValidation && 'withoutValidation'
      )}
    >
      <StyledLabel
        className={clsx('label', boldLabel && 'boldLabel', required && 'required')}
        labelWidth={labelWidth}
      >
        {label}
      </StyledLabel>
      {getInputWithoutLeftLabel()}
    </StyledFlexRow>
  ) : (
    getInputWithoutLeftLabel()
  )
})

Input.propTypes = {
  boldLabel: T.bool,
  disabled: T.bool,
  error: T.string,
  focusBorderColorKey: T.string,
  focusOutlineColorKey: T.string,
  fullWidth: T.bool,
  iconLeftProps: T.object,
  iconRightProps: T.object,
  label: T.string,
  labelType: T.oneOf(['top', 'left']),
  labelWidth: T.string,
  multiline: T.bool,
  name: T.string,
  onChange: T.func,
  onSubmit: T.func,
  placeholder: T.string,
  placeholderColor: T.string,
  size: T.oneOf(['small', 'medium', 'large']),
  success: T.bool,
  type: T.oneOf(['text', 'password', 'number', 'richText', 'time']),
  value: T.oneOfType([T.string, T.number, T.object]),
  variant: T.oneOf(['primary', 'secondary']),
  withBorder: T.bool,
  withDebounce: T.bool,
  withFocusBorder: T.bool,
  withFocusOutline: T.bool,
  withoutValidation: T.bool,
}

export default Input
