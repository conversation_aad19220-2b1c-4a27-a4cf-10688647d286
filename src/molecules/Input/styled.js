'use client'
import styled from 'styled-components'
import { pick } from 'dot-object'

import getTokens from '../../utils/getTokens'

export const StyledInputWrapper = styled.div`
  width: ${({ fullWidth }) => fullWidth && '100%'};

  &.withoutValidation {
    margin-bottom: 0;
  }

  &.hasError {
    margin-bottom: 10px;
  }
`

export const StyledWrapper = styled.div`
  position: relative;
  display: flex;
  flex-grow: 1;

  .labelLeft &,
  &.withoutValidation {
    margin-bottom: 0;
  }

  .rightIcon {
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;

    &.success {
      top: 35%;
      background-color: ${({ theme }) => theme.color.status.success};
      path {
        fill: ${({ theme }) => theme.color.general.light};
      }
    }
  }

  & > .icon.leftIcon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 15px;
  }

  width: ${({ fullWidth }) => fullWidth && '100%'};
`

export const StyledInput = styled.input`
  border: none;
  outline: none;
  width: 100%;
  flex-grow: 1;

  &.multiline {
    line-height: 22px;
  }

  .primary &,
  .secondary & {
    padding-right: 16px;
    padding-left: 16px;
    border: 1px solid transparent;

    &.multiline {
      line-height: 22px;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    &.withBorder {
      border: 1px solid ${({ theme }) => theme.color.general.gray2};
    }

    &:focus {
      border: 1px solid ${({ theme }) => theme.color.general.gray2};
    }

    &.withFocusBorder:focus {
      border: 1px solid ${({ theme, focusBorderColorKey }) => pick(focusBorderColorKey, theme.color)};
    }

    &.withFocusOutline:focus {
      outline: 2px solid ${({ theme, focusOutlineColorKey }) => pick(focusOutlineColorKey, theme.color)};
    }

    &.hasLeftIcon {
      padding-right: 16px;
      padding-left: 36px;
      &.hasRightIcon {
        padding-right: 36px;
      }
    }
    &.hasRightIcon {
      padding-right: 36px;
      padding-left: 16px;
      &.hasLeftIcon {
        padding-left: 36px;
      }
    }

    &.hasError {
      border: 1px solid ${({ theme }) => theme.color.status.error};
    }
    &.success {
      border: 1px solid ${({ theme }) => theme.color.status.success};
    }

    ${({ theme, size, variant }) => getTokens(`input-${variant}-black-${size}`, theme)};
  }

  .disabled & {
    background-color: ${({ theme }) => theme.color.general.gray1};
    border-color: ${({ theme }) => theme.color.general.gray2};
    color: ${({ theme, placeholderColor }) => placeholderColor || theme.color.general.gray3};
    cursor: not-allowed;
  }

  ::placeholder {
    opacity: 1;
    color: ${({ theme, placeholderColor }) => placeholderColor || theme.color.general.gray3};
  }

  :-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: ${({ theme, placeholderColor }) => placeholderColor || theme.color.general.gray3};
  }

  ::-ms-input-placeholder {
    /* Microsoft Edge */
    color: ${({ theme, placeholderColor }) => placeholderColor || theme.color.general.gray3};
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
    font-size: 16px !important;
  }
  //@media only screen and (max-width: 850px) {
  //  .secondary &,
  //  .primary & {
  //    font-size: 16px;
  //  }
  //}
`

export const StyledFlexRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 25px;

  &.withoutValidation {
    margin-bottom: 0;
  }
`

export const StyledLabel = styled.div`
  margin-bottom: 10px;
  font-family: ${({ theme }) => theme.font.family.primary};
  min-width: ${({ labelWidth }) => labelWidth};
  color: ${({ theme }) => theme.color.general.dark};

  .primary &,
  &.primary {
    font-size: ${({ theme: { components } }) => components.input.primary.black.large['font-size']};
    line-height: 20px;
  }
  .secondary &,
  &.secondary {
    font-size: ${({ theme: { components } }) => components.input.secondary.black.large['font-size']};
    line-height: 20px;
  }

  &.boldLabel {
    font-weight: ${({ theme }) => theme.font.weight.bold};
  }
  .labelLeft & {
    margin-right: 15px;
    margin-bottom: 0;
  }

  &.required::after {
    content: ' *';
    color: ${({ theme }) => theme.color.status.error};
  }
`

export const StyledErrorLabel = styled.div`
  ${({ theme }) => getTokens('typography-body2-black-large', theme)};
  color: ${({ theme }) => theme.color.status.error};
  padding-top: 5px;

  &:last-child {
    padding-bottom: 0;
  }

  .icon {
    margin-right: 5px;
  }
`
