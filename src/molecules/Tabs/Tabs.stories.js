import React from 'react'
import { boolean, number, select, text, withKnobs } from '@storybook/addon-knobs'

import Tabs from './Tabs'
import { LOREM_1, LOREM_FOURTH, LOREM_HALF, MENU_ALIGN_OPTIONS } from '../../constants/propTypes'

export default {
  title: 'Molecules/Tabs',
  component: Tabs,
}

export const tabs = () => {
  // const inverted = boolean('Inverted', false)
  return (
    <Tabs
      typographyType={text('Typography type', '')}
      color={text('Color', '')}
      backgroundColor={text('Background color', '')}
      // menuAlign={select('Tabs align', MENU_ALIGN_OPTIONS, 'left')}
      tabsTitles={[
        { id: '1', title: 'One' },
        { id: '2', title: 'Two' },
        { id: '3', title: 'Three' },
        { id: '4', title: 'Four' },
      ]}
      dropdownItems={[
        { id: '1', label: 'One' },
        { id: '2', label: 'Two' },
        { id: '3', label: 'Three' },
        { id: '4', label: 'Four' },
      ]}
      tabsContents={[LOREM_1, LOREM_HALF, LOREM_FOURTH, LOREM_1]}
      tabPadding={text('Tab padding', '')}
      withAddAction={boolean('With add action', true)}
      withUnderlineActive={boolean('With underline active', true)}
    />
  )
}

tabs.story = {
  decorators: [withKnobs],
}
