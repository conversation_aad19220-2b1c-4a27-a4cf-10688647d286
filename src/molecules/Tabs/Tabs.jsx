'use client'
import { PropTypes as T } from 'prop-types'
import React, { useEffect, useState } from 'react'
import { Tab, Tabs as TabsComponent } from 'react-tabs-scrollable'
import { withTheme } from 'styled-components'
import clsx from 'clsx'

import 'react-tabs-scrollable/dist/rts.css'

import FlexRow from '../../atoms/FlexRow/'
import Icon from '../../atoms/Icon/Icon'
import Dropdown from '../Dropdown'
import { StyledTabs } from './styled'

const Tabs = (
  {
    activeTabProp,
    backgroundColor,
    className,
    color,
    dropdownItems,
    errorTabIndex,
    isDisabled,
    typographyType,
    tabsTitles,
    tabsContents,
    tabPadding,
    onDropItemClick,
    onTabChange,
    withAddAction = true,
    withUnderlineActive,
  },
  props
) => {
  const [activeTab, setActiveTab] = useState(0)

  useEffect(() => {
    if (activeTabProp >= 0) {
      setActiveTab(activeTabProp)
    }
  }, [activeTabProp])

  useEffect(() => {
    if (errorTabIndex?.index >= 0) {
      setActiveTab(errorTabIndex.index)
    }
  }, [errorTabIndex])

  if (
    !tabsTitles?.length &&
    !tabsContents?.length &&
    !(!isDisabled && withAddAction && dropdownItems?.length)
  ) {
    return null
  }

  const onTabClick = (e, idx) => {
    setActiveTab(idx)
    if (onTabChange) {
      onTabChange(e, idx)
    }
  }

  return (
    <StyledTabs
      variant={typographyType || 'caption1'}
      color={color}
      backgroundColor={backgroundColor}
      className={clsx(className, withUnderlineActive && 'withUnderlineActive')}
      tabPadding={tabPadding}
    >
      <FlexRow margin="0 0 20px" gap="10px">
        {!isDisabled && withAddAction && !!dropdownItems?.length && (
          <Dropdown
            MenuButton={Icon}
            buttonProps={{
              name: 'plus2',
              wrapperWidth: 32,
              wrapperHeight: 32,
            }}
            openDirection="right"
            className="tabsDropdown"
            dropdownItems={dropdownItems}
            onItemClick={onDropItemClick}
          />
        )}
        <TabsComponent
          activeTab={activeTab}
          onTabClick={onTabClick}
          hideNavBtnsOnMobile={false}
          rightNavBtnClassName="right-nav-btn"
          rightBtnIcon={<Icon name="chevronLeft" />}
          leftBtnIcon={<Icon name="chevronLeft" />}
        >
          {tabsTitles.map((item, idx) => (
            <Tab key={idx} id={item.props?.id || item.id}>
              {item.title || item}
            </Tab>
          ))}
        </TabsComponent>
      </FlexRow>
      {tabsContents?.map((item, index) => {
        return (
          <div className="animate__animated animate__fadeInLeft" role="tabpanel" key={index} {...props}>
            {activeTab === index && <div className="mx-4">{item}</div>}
          </div>
        )
      })}
    </StyledTabs>
  )
}

export default withTheme(Tabs)

Tabs.propTypes = {
  typographyType: T.string,
  withAddAction: T.bool,
  withUnderlineActive: T.bool,
}
