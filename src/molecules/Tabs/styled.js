'use client'
import styled from 'styled-components'
import getTokens from '../../utils/getTokens'

export const StyledTabs = styled.div`
  .rts___tabs___container {
    padding: 0;
    * {
      ${({ variant, theme }) => getTokens(`typography-${variant}-black-large`, theme)};
      letter-spacing: 2px;
      color: ${({ color }) => color};
    }

    .rts___nav___btn {
      border: none;
      padding: 0;
      &:disabled.rts___btn svg path {
        stroke: ${({ backgroundColor, theme }) => backgroundColor || theme.color.general.gray3};
      }
      &.left-nav-btn {
        padding-right: 10px;
      }
      &.right-nav-btn {
        padding-left: 10px;
        svg {
          transform: rotate(180deg);
        }
      }

      &:hover {
        background: none;
        transition: none;
      }
    }
    .rts___tab {
      margin: 0;
      position: relative;
      background: none;
      box-shadow: none;
      border: none;
      border-radius: 0;
      padding: ${({ tabPadding }) => tabPadding || '8px 10px'};
      border-bottom: ${({ theme }) => `1px solid ${theme.color.general.gray1}`};

      &.rts___tab___selected {
        background-color: ${({ backgroundColor, theme }) => backgroundColor || theme.color.general.gray2};
        border-top-left-radius: ${({ theme }) => theme.size.border.radius.main};
        border-top-right-radius: ${({ theme }) => theme.size.border.radius.main};
      }
    }
  }

  &.withUnderlineActive {
    .rts___tab.rts___tab___selected {
      border-bottom: ${({ theme }) => `2px solid ${theme.color.primary.main}`};
    }
  }
`
