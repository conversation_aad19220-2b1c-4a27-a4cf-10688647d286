import { addToBasket } from './icons/addToBasket'
import { arrowLeft } from './icons/arrowLeft'
import { at } from './icons/at'
import { banners } from './icons/banners'
import { bill } from './icons/bill'
import { block } from './icons/block'
import { blueCheckBadge } from './icons/blueCheckBadge'
import { box } from './icons/box'
import { boxes } from './icons/boxes'
import { brand } from './icons/brand'
import { briefcase } from './icons/briefcase'
import { brush } from './icons/brush'
import { bulb } from './icons/bulb'
import { calendarAdd } from './icons/calendarAdd'
import { calendarEdit } from './icons/calendarEdit'
import { careChemicalClean } from './icons/careChemicalClean'
import { careDelicate40Degrees } from './icons/careDelicate40Degrees'
import { careDelicateCycle } from './icons/careDelicateCycle'
import { careDelicateWash } from './icons/careDelicateWash'
import { careDetergentDelicateFabrics } from './icons/careDetergentDelicateFabrics'
import { careDoNotTumbleDry } from './icons/careDoNotTumbleDry'
import { careIronMediumTemp } from './icons/careIronMediumTemp'
import { careMachineWashCold } from './icons/careMachineWashCold'
import { careNonChlorineBleach } from './icons/careNonChlorineBleach'
import { careTumbleDryLowTemp } from './icons/careTumbleDryLowTemp'
import { chart } from './icons/chart'
import { chartDummy } from './icons/chartDummy'
import { check } from './icons/check'
import { checkRounded } from './icons/checkRounded'
import { checkboxChecked } from './icons/checkboxChecked'
import { checkboxSquaredChecked } from './icons/checkboxSquaredChecked'
import { checkboxSquaredUnchecked } from './icons/checkboxSquaredUnchecked'
import { checkboxUnchecked } from './icons/checkboxUnchecked'
import { chevronDoubleLeft } from './icons/chevronDoubleLeft'
import { chevronDown } from './icons/chevronDown'
import { chevronDownFilled } from './icons/chevronDownFilled'
import { chevronLeft } from './icons/chevronLeft'
import { chevronUp } from './icons/chevronUp'
import { clients } from './icons/clients'
import { coefficient } from './icons/coefficient'
import { contactInfo } from './icons/contactInfo'
import { contracts } from './icons/contracts'
import { copyPaste } from './icons/copyPaste'
import { creditCard } from './icons/creditCard'
import { cross } from './icons/cross'
import { currency } from './icons/currency'
import { curvedArrowRight } from './icons/curvedArrowRight'
import { dashboard } from './icons/dashboard'
import { defaultFileType } from './icons/defaultFileType'
import { deliveryBox } from './icons/deliveryBox'
import { deliveryBoxes } from './icons/deliveryBoxes'
import { deliveryFolder } from './icons/deliveryFolder'
import { deliveryTruck } from './icons/deliveryTruck'
import { deliveryTruck2 } from './icons/deliveryTruck2'
import { docFileType } from './icons/docFileType'
import { dollarSign } from './icons/dollarSign'
import { download } from './icons/download'
import { draft } from './icons/draft'
import { emptyBasket } from './icons/emptyBasket'
import { envelope } from './icons/envelope'
import { error } from './icons/error'
import { eyeSign } from './icons/eyeSign'
import { eyeSignCross } from './icons/eyeSignCross'
import { facebook } from './icons/facebook'
import { facebookLetter } from './icons/facebookLetter'
import { facebookSign } from './icons/facebookSign'
import { faqs } from './icons/faqs'
import { file } from './icons/file'
import { fileCheck } from './icons/fileCheck'
import { fileCheckList } from './icons/fileCheckList'
import { fileDollar } from './icons/fileDollar'
import { fileFalse } from './icons/fileFalse'
import { fileQuestion } from './icons/fileQuestion'
import { fileSimple } from './icons/fileSimple'
import { filter } from './icons/filter'
import { filter2 } from './icons/filter2'
import { flag } from './icons/flag'
import { flagEN } from './icons/flagEN'
import { flagUK } from './icons/flagUK'
import { flowchart } from './icons/flowchart'
import { frangipani } from './icons/frangipani'
import { geometricFigures } from './icons/geometricFigures'
import { globe } from './icons/globe'
import { goodzykOO } from './icons/goodzykOO'
import { google } from './icons/google'
import { gridView } from './icons/gridView'
import { hamburgerMenu } from './icons/hamburgerMenu'
import { hamburgerMenu2 } from './icons/hamburgerMenu2'
import { hamburgerMenu3 } from './icons/hamburgerMenu3'
import { home } from './icons/home'
import { hospital } from './icons/hospital'
import { hourglass } from './icons/hourglass'
import { info } from './icons/info'
import { instagram } from './icons/instagram'
import { instagramSign } from './icons/instagramSign'
import { item } from './icons/item'
import { items } from './icons/items'
import { layers } from './icons/layers'
import { letter } from './icons/letter'
import { linkExternal } from './icons/linkExternal'
import { linkSign } from './icons/linkSign'
import { linkedinSign } from './icons/linkedinSign'
import { list } from './icons/list'
import { listBulleted } from './icons/listBulleted'
import { listNumber } from './icons/listNumber'
import { listView } from './icons/listView'
import { loading } from './icons/loading'
import { logout } from './icons/logout'
import { luggage } from './icons/luggage'
import { marker } from './icons/marker'
import { measurementUnits } from './icons/measurementUnits'
import { menuCloseToLeft } from './icons/menuCloseToLeft'
import { merchantsTent } from './icons/merchantsTent'
import { meter } from './icons/meter'
import { minus } from './icons/minus'
import { minus2 } from './icons/minus2'
import { moneyBox } from './icons/moneyBox'
import { navMenuToggleArrow } from './icons/navMenuToggleArrow'
import { newStatus } from './icons/newStatus'
import { noDataFound } from './icons/noDataFound'
import { noImage } from './icons/noImage'
import { noNewNotification } from './icons/noNewNotification'
import { notificationOff } from './icons/notificationOff'
import { notificationOn } from './icons/notificationOn'
import { notAdded } from './icons/notAdded'
import { notFound } from './icons/notFound'
import { page404 } from './icons/page404'
import { pageComingSoon } from './icons/pageComingSoon'
import { pages } from './icons/pages'
import { pagesMenu } from './icons/pagesMenu'
import { pdfFileType } from './icons/pdfFileType'
import { pencil } from './icons/pencil'
import { pin } from './icons/pin'
import { planet } from './icons/planet'
import { platforms } from './icons/platforms'
import { plus } from './icons/plus'
import { plus2 } from './icons/plus2'
import { print } from './icons/print'
import { profile } from './icons/profile'
import { qLetter } from './icons/qLetter'
import { question } from './icons/question'
import { radioChecked } from './icons/radioChecked'
import { radioUnchecked } from './icons/radioUnchecked'
import { reload } from './icons/reload'
import { rightPanelNarrow } from './icons/rightPanelNarrow'
import { rightPanelWide } from './icons/rightPanelWide'
import { ruler } from './icons/ruler'
import { search } from './icons/search'
import { send } from './icons/send'
import { serverError } from './icons/serverError'
import { settings } from './icons/settings'
import { settingsAudio } from './icons/settingsAudio'
import { settingsCard } from './icons/settingsCard'
import { share } from './icons/share'
import { shoppingBag } from './icons/shoppingBag'
import { shoppingBasket } from './icons/shoppingBasket'
import { shoppingCart } from './icons/shoppingCart'
import { shoppingCart2 } from './icons/shoppingCart2'
import { signal } from './icons/signal'
import { sizeL } from './icons/sizeL'
import { sizeL_XL } from './icons/sizeL_XL'
import { sizeM } from './icons/sizeM'
import { sizeM_L } from './icons/sizeM_L'
import { sizeM_XL } from './icons/sizeM_XL'
import { sizeONE_SIZE } from './icons/sizeOne_Size'
import { sizeS } from './icons/sizeS'
import { sizeS_M } from './icons/sizeS_M'
import { sizeXL } from './icons/sizeXL'
import { sizeXS } from './icons/sizeXS'
import { sizeXS_S } from './icons/sizeXS_S'
import { sizeXXL } from './icons/sizeXXL'
import { sizeXXS } from './icons/sizeXXS'
import { sizeXXXL } from './icons/sizeXXXL'
import { sort } from './icons/sort'
import { squares } from './icons/squares'
import { star } from './icons/star'
import { stateDeleted } from './icons/stateDeleted'
import { stateDrafted } from './icons/stateDrafted'
import { statePosted } from './icons/statePosted'
import { status } from './icons/status'
import { statusActive } from './icons/statusActive'
import { statusCanceled } from './icons/statusCanceled'
import { statusCompleted } from './icons/statusCompleted'
import { statusConfirmed } from './icons/statusConfirmed'
import { statusDeleted } from './icons/statusDeleted'
import { statusNew } from './icons/statusNew'
import { statusPaid } from './icons/statusPaid'
import { statusPending } from './icons/statusPending'
import { statusShipped } from './icons/statusShipped'
import { tShirt } from './icons/tShirt'
import { tShirtSlide } from './icons/tShirtSlide'
import { tag } from './icons/tag'
import { tags } from './icons/tags'
import { target } from './icons/target'
import { telegram } from './icons/telegram'
import { thankYouBalloons } from './icons/thankYouBalloons'
import { threeDotsVertical } from './icons/threeDotsVertical'
import { thumbUpDown } from './icons/thumbUpDown'
import { time } from './icons/time'
import { toolbox } from './icons/toolbox'
import { trashBin } from './icons/trashBin'
import { trashBin2 } from './icons/trashBin2'
import { twitter } from './icons/twitter'
import { twitterSign } from './icons/twitterSign'
import { twoRowsView } from './icons/twoRowsView'
import { txtFileType } from './icons/txtFileType'
import { upload } from './icons/upload'
import { user } from './icons/user'
import { users } from './icons/users'
import { usersPlus } from './icons/usersPlus'
import { viber } from './icons/viber'
import { warehouse } from './icons/warehouse'
import { warehouses } from './icons/warehouses'
import { warning } from './icons/warning'
import { whatsapp } from './icons/whatsapp'
import { xlsFileType } from './icons/xlsFileType'
import { youtubeSign } from './icons/youtubeSign'
import { zoomArrow } from './icons/zoomArrow'
import { weightKg } from './icons/weightKg'

//size...sq - icon 22x22, small text inside
const Icons = {
  addToBasket,
  arrowLeft,
  at,
  banners,
  bill,
  block,
  blueCheckBadge,
  box,
  boxes,
  brand,
  briefcase,
  brush,
  bulb,
  calendarAdd,
  calendarEdit,
  careChemicalClean,
  careDelicate40Degrees,
  careDelicateCycle,
  careDelicateWash,
  careDetergentDelicateFabrics,
  careDoNotTumbleDry,
  careIronMediumTemp,
  careMachineWashCold,
  careNonChlorineBleach,
  careTumbleDryLowTemp,
  chart,
  chartDummy,
  check,
  checkRounded,
  checkboxChecked,
  checkboxSquaredChecked,
  checkboxSquaredUnchecked,
  checkboxUnchecked,
  chevronDoubleLeft,
  chevronDown,
  chevronDownFilled,
  chevronLeft,
  chevronUp,
  clients,
  coefficient,
  contactInfo,
  contracts,
  copyPaste,
  creditCard,
  cross,
  currency,
  curvedArrowRight,
  dashboard,
  defaultFileType,
  deliveryBox,
  deliveryBoxes,
  deliveryFolder,
  deliveryTruck,
  deliveryTruck2,
  docFileType,
  dollarSign,
  download,
  draft,
  emptyBasket,
  envelope,
  error,
  eyeSign,
  eyeSignCross,
  facebook,
  facebookLetter,
  facebookSign,
  faqs,
  file,
  fileSimple,
  fileCheck,
  fileCheckList,
  fileDollar,
  fileFalse,
  fileQuestion,
  filter,
  filter2,
  flag,
  flagEN,
  flagUK,
  flowchart,
  frangipani,
  geometricFigures,
  globe,
  goodzykOO,
  google,
  gridView,
  hamburgerMenu,
  hamburgerMenu2,
  hamburgerMenu3,
  home,
  hospital,
  hourglass,
  info,
  instagram,
  instagramSign,
  item,
  items,
  layers,
  letter,
  linkSign,
  linkedinSign,
  linkExternal,
  list,
  listBulleted,
  listNumber,
  listView,
  loading,
  logout,
  luggage,
  marker,
  measurementUnits,
  menuCloseToLeft,
  merchantsTent,
  meter,
  minus,
  minus2,
  moneyBox,
  navMenuToggleArrow,
  newStatus,
  noDataFound,
  noImage,
  noNewNotification,
  notificationOff,
  notificationOn,
  notAdded,
  notFound,
  page404,
  pageComingSoon,
  pages,
  pagesMenu,
  pencil,
  pdfFileType,
  pin,
  planet,
  platforms,
  plus,
  plus2,
  print,
  profile,
  qLetter,
  question,
  radioChecked,
  radioUnchecked,
  reload,
  rightPanelNarrow,
  rightPanelWide,
  ruler,
  search,
  send,
  serverError,
  settings,
  settingsAudio,
  settingsCard,
  share,
  shoppingBag,
  shoppingBasket,
  shoppingCart,
  shoppingCart2,
  signal,
  sizeL,
  sizeL_XL,
  // sizeL_XLsq,
  sizeM,
  sizeM_L,
  // sizeM_Lsq,
  sizeM_XL,
  // sizeM_XLsq,
  sizeONE_SIZE,
  sizeS,
  sizeS_M,
  // sizeS_Msq,
  sizeXL,
  sizeXS,
  sizeXS_S,
  // sizeXS_Ssq,
  sizeXXL,
  sizeXXS,
  // sizeXXSsq,
  sizeXXXL,
  // sizeXXXLsq,
  sort,
  squares,
  star,
  stateDeleted,
  stateDrafted,
  statePosted,
  status,
  statusActive,
  statusCanceled,
  statusCompleted,
  statusConfirmed,
  statusDeleted,
  statusNew,
  statusPaid,
  statusPending,
  statusShipped,
  tShirt,
  tShirtSlide,
  tag,
  tags,
  target,
  telegram,
  thankYouBalloons,
  threeDotsVertical,
  thumbUpDown,
  time,
  toolbox,
  trashBin,
  trashBin2,
  twitter,
  twitterSign,
  txtFileType,
  twoRowsView,
  upload,
  user,
  users,
  usersPlus,
  viber,
  warning,
  warehouse,
  warehouses,
  weightKg,
  whatsapp,
  xlsFileType,
  youtubeSign,
  zoomArrow,
}

export default Icons
