export const ITEM_TO_API_KEYS = {
  attributeType: 'attributeTypes',
  brand: 'brands',
  carrier: 'carriers',
  category: 'categories',
  categoriesType: 'categoriesTypes',
  client: 'organizations',
  country: 'countries',
  organization: 'organizations',
  customer: 'organizations',
  enumeration: 'enumerations',
  industry: 'industries',
  item: 'items',
  magnitude: 'magnitudes',
  merchant: 'organizations',
  page: 'pages',
  platform: 'platforms',
  role: 'roles',
  state: 'states',
  status: 'statuses',
  stock: 'stocks',
  warehouse: 'warehouses',
  type: 'attributeTypes', //TODO: change "type" to smth more specific
  unit: 'units',
}

export const DATA_TYPE_TO_SINGULAR_FORM = {
  attributes: 'attribute',
  banners: 'banner',
  brands: 'brand',
  carriers: 'carrier',
  categories: 'category',
  clients: 'client',
  countries: 'country',
  currencies: 'currency',
  enumerations: 'enumeration',
  industries: 'industry',
  items: 'item',
  merchants: 'merchant',
  orders: 'order',
  pages: 'page',
  platforms: 'platform',
  roles: 'role',
  'stock-items': 'stock-item',
  stocks: 'stock',
  warehouses: 'warehouse',
  modifications: 'modification',
  units: 'unit',
  users: 'user',
}

export const MAP_ITEM_SELECTION_PROPS_TO_API_KEYS = {
  categories: 'general.category.id',
  brands: 'general.brand.id',
  tags: 'general.tags',
}

export const PLATFORM_CHOSEN_COLLECTIONS = {
  // carriers: true,
  // categories: true,
  // demands: true,
  // currencies: true,
}

export const DATA_KEYS_WITH_VERSIONS_FOR_LOCAL_STORAGE = {
  // attributes: true,
  // brands: true,
  // categories: true,
  // countries: true,
  // currencies: true,
  // industries: true,
}
