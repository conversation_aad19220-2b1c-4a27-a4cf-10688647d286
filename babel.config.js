module.exports = {
  env: {
    development: {
      presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
      plugins: [
        ['styled-components', { ssr: false, displayName: true }],
        [
          'file-loader',
          {
            name: '[path][name].[ext]',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'svg'],
            outputPath: '/build',
            context: './src',
            limit: Infinity,
          },
        ],
      ],
    },
    production: {
      presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
      plugins: [
        ['styled-components', { ssr: false, displayName: true }],
        [
          'file-loader',
          {
            name: '[path][name].[ext]',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'svg'],
            outputPath: '/build',
            context: './src',
            limit: Infinity,
          },
        ],
      ],
    },
  },
}
