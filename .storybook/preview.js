import React, { createContext } from 'react'
import { ThemeProvider } from 'styled-components'
import { MINIMAL_VIEWPORTS } from '@storybook/addon-viewport'

// import '../../crm/src/fonts.css'

import { useScreenContext } from '../src/hooks/useScreenContext'
import { CommonGlobalStyle } from '../src'
import theme from './theme'

export const ScreenContext = createContext(undefined)

const screenDecorator = (StoryFn) => {
  const screenContextValues = useScreenContext(theme)

  return (
    <ScreenContext.Provider value={screenContextValues}>
      <StoryFn />
    </ScreenContext.Provider>
  )
}

export const decorators = [
  (Story) => (
    <ThemeProvider theme={theme}>
      <CommonGlobalStyle />
      {Story()}
    </ThemeProvider>
  ),
  screenDecorator,
]

export const parameters = {
  decorators,
  viewport: {
    viewports: MINIMAL_VIEWPORTS,
    // defaultViewport: 'someDefault',
  },
}
