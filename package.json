{"name": "@aidsupply/components", "version": "2.21.18", "main": "build/index.js", "module": "build/index.js", "scripts": {"type-check": "tsc --noEmit", "build": "babel --root-mode upward src -d build --ignore **/*.story.js,**/*.spec.js,**/*.story.ts,**/*.spec.ts --extensions \".ts,.tsx,.js,.jsx\"", "prepare": "npm run build", "predev": "npm run build", "dev": "STORYBOOK_NAME=${APP_NAME:-crm} storybook dev -s ./src/static -p 6660", "watch": "babel --root-mode upward src --watch -d build --ignore **/*.story.js,**/*.spec.js,**/*.story.ts,**/*.spec.ts --extensions \".ts,.tsx,.js,.jsx\""}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "dependencies": {"add": "^2.0.6", "body-scroll-lock": "^3.0.3", "clsx": "1.2.1", "dayjs": "^1.11.8", "dot-object": "^2.1.4", "html-react-parser": "^5.1.2", "imagekitio-react": "^1.0.10", "js-regex-pl": "^1.0.1", "nanoid": "^3.1.22", "prop-types": "^15.7.2", "rc-slider": "^11.1.8", "react": "^18.3.1", "react-base-table": "^1.13.0", "react-datepicker": "^8.2.0", "react-dom": "^18.3.1", "react-multi-carousel": "^2.5.5", "react-overlays": "^5.1.1", "react-phone-number-input": "3.1.0", "react-player": "^1.14.2", "react-quill-new": "^3.4.0", "react-rte": "^0.16.5", "react-select": "^5.10.1", "react-switch": "^7.1.0", "react-tabs-scrollable": "^2.0.8", "react-texty": "^0.6.0", "react-virtualized-auto-sizer": "^1.0.2", "react-window": "^1.8.6", "react-window-infinite-loader": "^1.0.5", "sane-email-validation": "^3.0.1", "styled-components": "^5.3.3", "transliteration": "^2.2.0"}, "resolutions": {"styled-components": "^5"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/aidsupply/components.git"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.26.0", "@storybook/addon-knobs": "^7.0.2", "@storybook/addon-viewport": "^7.6.13", "@storybook/react-webpack5": "^7.6.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/styled-components": "^5.1.34", "babel-loader": "^9.0.0", "babel-plugin-file-loader": "^2.0.0", "babel-plugin-styled-components": "^2.0.7", "eslint": "^8.54.0", "html-webpack-plugin": "^5.5.0", "storybook": "^7.6.13", "typescript": "^5.8.2"}}